# PC FileLoader to Lakeflow Migration Guide

## Overview

This document explains the migration from the traditional `PC With FileLoader.py` notebook to the modern Lakeflow declarative pipeline approach.

## What Was Migrated

### Original PC With FileLoader.py
The original notebook was a complex Python script that:
- Read configuration from `pipelinecontrol` table
- Processed cleaned files from various XVA sources
- Created Bronze tables (raw data)
- Created Silver tables (cleaned, deduplicated data)
- Handled error logging and retry logic
- Supported both file-based and Oracle database sources

### New Lakeflow Pipeline: `pc_fileloader_declarative.py`

The new pipeline provides the same functionality using Lakeflow's declarative approach:

#### **Configuration Management**
- **Table**: `pipeline_config` - Reads active configurations from pipelinecontrol table
- **Parameters**: entity=XVA, env=dev (configurable via pipeline settings)

#### **Bronze Layer Tables** (Raw Data)
1. `bronze_xva_consolidated_report` - XVA Consolidated Report data
2. `bronze_xva_input_report` - XVA Input Report data  
3. `bronze_xva_mtm_reconciliation` - XVA MTM Reconciliation data
4. `bronze_xva_missing_trades` - XVA Missing Trades data
5. `bronze_trade_data_reconciliation` - Trade Data Reconciliation
6. `bronze_metal_exposures` - Metal Exposures data
7. `bronze_energy_exposures` - Energy Exposures data

#### **Silver Layer Tables** (Cleaned Data)
1. `silver_xva_consolidated_report` - Cleaned XVA Consolidated Report
2. `silver_xva_input_report` - Cleaned XVA Input Report
3. `silver_xva_mtm_reconciliation` - Cleaned XVA MTM Reconciliation
4. `silver_xva_missing_trades` - Cleaned XVA Missing Trades
5. `silver_trade_data_reconciliation` - Cleaned Trade Data Reconciliation

#### **Monitoring & Quality**
- `processing_metrics` - Pipeline execution metrics
- `data_quality_summary` - Data quality checks and expectations

## Key Features

### 🔄 **Auto Loader Integration**
- Uses Databricks Auto Loader for efficient file detection
- Supports CSV, Parquet, and Excel files
- Schema evolution and inference
- Incremental processing

### 📊 **Data Quality**
- Built-in DLT expectations for data validation
- Automatic column name cleaning
- Record-level quality scoring
- Data freshness checks

### 🏗️ **Declarative Architecture**
- No complex Python loops or threading
- Automatic dependency management
- Built-in retry and error handling
- Simplified monitoring and debugging

### 📈 **Performance Optimizations**
- Delta table optimizations enabled
- Streaming processing where possible
- Efficient schema management
- Automatic table maintenance

## File Structure Mapping

### Source Files Location
```
/Volumes/dbw_dev_itdev_test_uks/bronze_xva/inbound_cleanfiles/XVA/
├── xvaconsolidatedreport/
├── xvacurateddataset_inputreport/
├── xvacurateddataset_mtmreconciliation/
├── xvacurateddataset_missingtradesreport/
├── tradedatareconciliation/
├── metalexposures/
└── energyexposures/
```

### Target Tables
```
Bronze Layer: mbcl_dev_bronze.xva.*
Silver Layer: mbcl_dev_silver.xva.*
```

## Migration Benefits

### ✅ **Advantages of Lakeflow Approach**
1. **Simplified Maintenance** - Declarative syntax is easier to understand and maintain
2. **Built-in Monitoring** - Automatic pipeline monitoring and alerting
3. **Better Error Handling** - DLT handles retries and error recovery automatically
4. **Schema Evolution** - Automatic schema changes without manual intervention
5. **Performance** - Optimized for streaming and batch processing
6. **Data Quality** - Built-in expectations and quality checks
7. **Lineage** - Automatic data lineage tracking

### ⚠️ **Considerations**
1. **Excel Files** - Limited streaming support for Excel files (fallback to CSV processing)
2. **Complex Logic** - Some custom business logic may need to be adapted
3. **Oracle Sources** - File-based sources only (Oracle sources would need separate pipeline)

## Usage Instructions

### 1. Deploy the Pipeline
```bash
# Upload the notebook to your workspace
# Create a DLT pipeline using the notebook
# Configure pipeline settings:
# - entity: XVA
# - env: dev
```

### 2. Configure Pipeline Settings
```json
{
  "configuration": {
    "entity": "XVA",
    "env": "dev"
  },
  "target": "mbcl_dev_bronze",
  "storage_location": "/Volumes/dbw_dev_itdev_test_uks/bronze_xva/pipeline_storage/"
}
```

### 3. Monitor Pipeline
- Use DLT UI for pipeline monitoring
- Check `processing_metrics` table for execution stats
- Review `data_quality_summary` for quality issues

### 4. Validate Results
```sql
-- Check bronze table counts
SELECT 'bronze_xva_consolidated_report' as table_name, COUNT(*) as row_count 
FROM bronze_xva_consolidated_report
UNION ALL
SELECT 'bronze_xva_input_report', COUNT(*) FROM bronze_xva_input_report
-- ... etc

-- Check data quality
SELECT * FROM data_quality_summary 
WHERE status != 'PASS'
ORDER BY check_timestamp DESC;
```

## Next Steps

1. **Test the Pipeline** - Run with sample data to validate functionality
2. **Performance Tuning** - Adjust cluster settings and optimization parameters
3. **Monitoring Setup** - Configure alerts and monitoring dashboards
4. **Documentation** - Update operational procedures and runbooks
5. **Training** - Train team on Lakeflow pipeline management

## Support

For issues or questions:
- Check DLT pipeline logs in Databricks UI
- Review data quality summary tables
- Consult Lakeflow documentation
- Contact the data engineering team
