# Inbound File Processing - Lakeflow Declarative Pipeline

This directory contains a comprehensive Lakeflow Declarative Pipeline implementation that replaces the original `Inbound_Files.py` notebook with a modern, scalable, and cost-effective solution using Auto Loader in triggered mode.

## Overview

The pipeline automatically detects new files dropped into the source location and processes them according to the same business logic as the original notebook, but with enhanced features:

- **Auto Loader Integration**: Automatically detects new files using file notifications
- **Triggered Mode**: Cost-effective execution that runs only when needed
- **Data Quality Expectations**: Built-in data validation and quality checks
- **Comprehensive Logging**: Detailed audit trail of all file operations
- **Error Handling**: Robust error handling with retry mechanisms
- **Scalability**: Automatic scaling based on workload

## Files Description

### Core Pipeline Files

1. **`inbound_file_processor_pipeline.py`** - Main Lakeflow pipeline notebook
   - Contains all DLT table definitions
   - Implements Auto Loader for file detection
   - Includes file processing logic and data quality expectations

2. **`inbound_file_processor_pipeline_config.json`** - Pipeline configuration
   - Defines pipeline settings and parameters
   - Configures triggered mode and Auto Loader options

3. **`inbound_file_processor_job.yml`** - Databricks job configuration
   - Defines the job that runs the pipeline
   - Includes scheduling and notification settings

## Key Features

### Auto Loader Configuration
- **File Notifications**: Uses Azure Event Grid for efficient file detection
- **Incremental Processing**: Only processes new files, not existing ones
- **Schema Evolution**: Automatically handles schema changes
- **Checkpoint Management**: Maintains processing state for exactly-once delivery

### Data Quality Expectations
- File name validation
- File size checks (must be > 0 bytes, < 1GB)
- File extension validation (csv, txt, xlsx, parquet)
- Recent file checks (within 30 days)
- Status validation for processing logs

### Tables Created

1. **`raw_file_events`** (Bronze)
   - Raw file detection events from Auto Loader
   - Contains file metadata and detection timestamps

2. **`processed_file_metadata`** (Bronze)
   - File metadata with cleaned names and target paths
   - Includes business logic for file name cleaning

3. **`enhanced_file_processing_log`** (Silver)
   - Comprehensive audit log compatible with original notebook
   - Tracks success/failure of file operations

4. **`pipeline_control_integration`** (Silver)
   - Bridge table that integrates with existing `pipelinecontrol` system
   - Maintains compatibility with existing monitoring dashboards

5. **`file_processing_summary`** (Gold)
   - Daily summary statistics with pipeline control compatibility
   - Success rates and processing volumes

6. **`pipeline_control_status_view`** (View)
   - Unified view combining Lakeflow metrics with pipeline control data
   - Seamless integration with existing monitoring systems

## Pipeline Control Integration

### Existing System Compatibility
The pipeline integrates with your existing `pipelinecontrol` table to maintain compatibility with current monitoring and control systems:

- **Reads Configuration**: Gets pipeline settings from `pipelinecontrol` table
- **Updates Status**: Maintains real-time status updates in `pipelinecontrol`
- **Unified Monitoring**: Provides views that combine Lakeflow metrics with existing control data

### Pipeline Control Functions
```python
# Read configuration from existing pipelinecontrol table
get_pipeline_control_config()

# Update status in pipelinecontrol table for monitoring
update_pipeline_control_status(status, description, error_message)
```

### Status Updates
The pipeline automatically updates the `pipelinecontrol` table with:
- **Starting**: When pipeline initializes
- **Running**: During normal file monitoring
- **Processing**: When actively processing files
- **Warning**: When some files fail processing
- **Completed**: When batch processing completes successfully

## Deployment Instructions

### Prerequisites
1. Unity Catalog enabled workspace
2. Serverless compute enabled (recommended)
3. Appropriate permissions for:
   - Creating pipelines
   - Creating jobs
   - Reading from source storage account
   - Writing to destination volumes

### Step 1: Create the Pipeline

1. Navigate to **Workflows** > **Delta Live Tables** in your Databricks workspace
2. Click **Create Pipeline**
3. Configure the pipeline:
   - **Name**: `inbound_file_processor_pipeline_dev`
   - **Source Code**: Upload or reference `inbound_file_processor_pipeline.py`
   - **Target**: Select your target catalog and schema
   - **Pipeline Mode**: **Triggered** (not Continuous)
   - **Serverless**: Enable if available

### Step 2: Configure Pipeline Parameters

Add these configuration parameters in the pipeline settings:

```json
{
  "pipeline.env": "dev",
  "pipeline.entity": "XVA",
  "pipelines.trigger.interval": "5 minutes",
  "spark.databricks.delta.autoOptimize.optimizeWrite": "true",
  "spark.databricks.delta.autoOptimize.autoCompact": "true"
}
```

### Step 3: Set Up File Notifications (Recommended)

For optimal performance, configure Azure Event Grid notifications:

1. In your Azure Storage Account, go to **Events**
2. Create an Event Subscription for blob creation events
3. Configure the endpoint to point to your Databricks workspace
4. Update the pipeline configuration to use notifications:
   ```json
   {
     "cloudFiles.useNotifications": "true"
   }
   ```

### Step 4: Create and Schedule the Job

1. Use the provided `inbound_file_processor_job.yml` file
2. Deploy using Databricks CLI or Terraform:
   ```bash
   databricks bundle deploy --target dev
   ```
3. Or manually create the job in the Databricks UI using the YAML as reference

### Step 5: Test the Pipeline

1. Drop a test file into your source directory
2. Trigger the pipeline manually or wait for the scheduled run
3. Monitor the pipeline execution in the DLT UI
4. Verify files are processed and moved correctly
5. Check the logging tables for audit information

## Monitoring and Troubleshooting

### Unified Monitoring with Pipeline Control
The pipeline integrates seamlessly with your existing `pipelinecontrol` monitoring system:

```sql
-- Check current pipeline status (unified view)
SELECT * FROM your_catalog.your_schema.pipeline_control_status_view
ORDER BY EntityId;

-- Monitor pipeline control table directly for XVA_REFACTORED
SELECT EntityId, SourceTableName, TargetTableName, Status,
       last_run_status, last_run_timestamp, Description, DateLogged
FROM dbw_dev_itdev_test_uks.utility_metadata.pipelinecontrol
WHERE Entity = 'XVA_REFACTORED'
AND Source = 'File'
AND SourceType = 'csv'
ORDER BY EntityId;

-- Check detailed file processing logs
SELECT * FROM your_catalog.your_schema.enhanced_file_processing_log
ORDER BY DateOperation DESC
LIMIT 100;

-- Monitor specific table processing status
SELECT SourceTableName, Status, last_run_status, Description
FROM dbw_dev_itdev_test_uks.utility_metadata.pipelinecontrol
WHERE Entity = 'XVA_REFACTORED'
AND SourceTableName = 'vwdeltapv'  -- Replace with specific table name
AND IsActive = true;
```

### Pipeline Monitoring
- **DLT UI**: Monitor pipeline health and performance
- **Pipeline Control Table**: Check status in existing monitoring dashboards
- **Unified Status View**: Use `pipeline_control_status_view` for comprehensive monitoring
- **File Processing Summary**: Check daily statistics and success rates
- **Enhanced Logs**: Monitor detailed operation logs for troubleshooting

### Status Indicators
- **Starting**: Pipeline is initializing
- **Running**: Normal operation, monitoring for files
- **Processing**: Actively processing files
- **Warning**: Some files failed processing (check logs)
- **Completed**: All files processed successfully

### Common Issues
1. **Permission Errors**: Ensure the pipeline has read access to source and write access to destination
2. **Pipeline Control Access**: Verify access to `mbcl_{env}_utility.metadata.pipelinecontrol` table
3. **File Notification Issues**: Verify Event Grid configuration if using notifications
4. **Schema Evolution**: Monitor for schema changes in source files

### Performance Tuning
- Adjust `pipelines.trigger.interval` based on your file arrival frequency
- Configure `cloudFiles.maxFilesPerTrigger` for batch processing control
- Use `cloudFiles.maxBytesPerTrigger` to control processing volume per batch

## Migration from Original Notebook

This pipeline provides the same functionality as the original `Inbound_Files.py` notebook with these improvements:

1. **Automatic Triggering**: No need for manual execution or complex scheduling
2. **Better Error Handling**: Built-in retry mechanisms and error logging
3. **Data Quality**: Automatic validation of file properties
4. **Scalability**: Automatic scaling based on workload
5. **Cost Efficiency**: Triggered mode reduces compute costs
6. **Monitoring**: Enhanced observability and logging

The `enhanced_file_processing_log` table maintains the same schema as the original notebook's logging for backward compatibility.

## Support

For issues or questions, contact the Data Engineering team or refer to the official Databricks documentation on Lakeflow Declarative Pipelines.
