# Databricks notebook source
# MAGIC %md
# MAGIC # Inbound File Processing - Lakeflow Declarative Pipeline
# MAGIC 
# MAGIC This pipeline processes inbound files using Auto Loader in triggered mode.
# MAGIC It monitors the source directory for new files and processes them automatically.
# MAGIC 
# MAGIC ## Features:
# MAGIC - Auto Loader for file detection and processing
# MAGIC - Triggered mode for cost-effective execution
# MAGIC - File name cleaning and standardization
# MAGIC - Automatic directory creation based on file names
# MAGIC - Comprehensive logging and error handling
# MAGIC - Data quality expectations

# COMMAND ----------

import dlt
import re
from datetime import datetime
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from pyspark.sql.types import *
import os

# COMMAND ----------

# MAGIC %md
# MAGIC ## Configuration and Parameters

# COMMAND ----------

# Get pipeline parameters - these will be set in the pipeline configuration
env = spark.conf.get("pipeline.env", "dev").lower()
entity = spark.conf.get("pipeline.entity", "").upper()

# Define source and destination directories based on environment
if env == "dev":
    source_directory = "/Volumes/dbw_dev_itdev_test_uks/bronze_xva/inbound_files/"
elif env == "prod":
    source_directory = "/Volumes/dbw_dev_itdev_test_uks/bronze_xva/inbound_files/"
else:
    raise ValueError(f"Unknown environment: {env}")

destination_directory = f"/Volumes/dbw_dev_itdev_test_uks/bronze_xva/inbound_cleanfiles/"

# Compile regular expressions for file name cleaning
date_pattern = re.compile(r'\d-\d-\dT\d:\d:\d')
non_alphanumeric_pattern = re.compile(r'[^a-zA-Z0-9]')
numeric_pattern = re.compile(r'\d')
non_alpha_pattern = re.compile(r'[^a-zA-Z]')

# COMMAND ----------

# MAGIC %md
# MAGIC ## File Processing Functions

# COMMAND ----------

def clean_file_name(file_name):
    """Clean and standardize file names according to business rules"""
    first_part = file_name[:19]
    
    if date_pattern.match(first_part):
        file_name = file_name[20:]
        first_part = ''
    else:
        first_part = non_alphanumeric_pattern.sub('', file_name[:15])
    
    has_number = any(char.isdigit() for char in first_part)
    
    if not has_number:
        parts = file_name.split('_')
        if len(parts) > 2:
            parts[2] = numeric_pattern.sub('', parts[2])
            file_name = '_'.join(parts)
        else:
            file_name = '_'.join(parts)
    
    if has_number:
        first_part += '_'
    
    second_part = non_alpha_pattern.sub('', file_name)
    cleaned_name = second_part
    cleaned_file_name = cleaned_name + '.csv'
    
    return cleaned_file_name

def extract_directory_name(cleaned_file_name):
    """Extract directory name from cleaned file name"""
    if cleaned_file_name.endswith('.csv'):
        cleaned_file_name = cleaned_file_name[:-4]

    dir_name = numeric_pattern.sub('', cleaned_file_name)
    dir_name = dir_name.replace(' ', '')
    dir_name = non_alpha_pattern.sub('', dir_name)
    
    return dir_name.lower()

# Register UDFs for use in Spark SQL
spark.udf.register("clean_file_name", clean_file_name, StringType())
spark.udf.register("extract_directory_name", extract_directory_name, StringType())

# COMMAND ----------

# MAGIC %md
# MAGIC ## Auto Loader Streaming Table for File Detection

# COMMAND ----------

@dlt.table(
    name="raw_file_events",
    comment="Raw file events detected by Auto Loader from the inbound directory",
    table_properties={
        "quality": "bronze",
        "pipelines.autoOptimize.managed": "true"
    }
)
def raw_file_events():
    """
    Streaming table that monitors the source directory for new files using Auto Loader.
    This table captures file metadata and triggers processing when new files arrive.
    """
    return (
        spark.readStream
        .format("cloudFiles")
        .option("cloudFiles.format", "binaryFile")  # Use binaryFile to get metadata
        .option("cloudFiles.useNotifications", "true")  # Enable file notifications for better performance
        .option("cloudFiles.includeExistingFiles", "false")  # Only process new files
        .option("cloudFiles.validateOptions", "true")
        .option("cloudFiles.schemaLocation", f"{destination_directory}/_schemas/raw_file_events")
        .load(source_directory)
        .select(
            col("path").alias("source_path"),
            col("modificationTime").alias("file_modification_time"),
            col("length").alias("file_size_bytes"),
            regexp_extract(col("path"), r"([^/]+)$", 1).alias("original_file_name"),
            current_timestamp().alias("detected_at"),
            lit(env).alias("environment"),
            lit(entity).alias("entity")
        )
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## File Processing and Cleaning Logic

# COMMAND ----------

@dlt.table(
    name="processed_file_metadata",
    comment="Metadata for processed files with cleaned names and target paths",
    table_properties={
        "quality": "bronze",
        "pipelines.autoOptimize.managed": "true"
    }
)
@dlt.expect_or_fail("valid_file_name", "original_file_name IS NOT NULL AND original_file_name != ''")
@dlt.expect_or_fail("valid_source_path", "source_path IS NOT NULL AND source_path != ''")
@dlt.expect_or_fail("valid_file_size", "file_size_bytes > 0")
@dlt.expect_or_fail("valid_cleaned_name", "cleaned_file_name IS NOT NULL AND cleaned_file_name != ''")
@dlt.expect_or_fail("valid_folder_name", "folder_name IS NOT NULL AND folder_name != ''")
@dlt.expect("supported_file_extension", "original_file_name RLIKE '\\.(csv|txt|xlsx|parquet)$'")
@dlt.expect("reasonable_file_size", "file_size_bytes < 1073741824")  # Less than 1GB
@dlt.expect("recent_file", "file_modification_time > current_timestamp() - INTERVAL 30 DAYS")
def processed_file_metadata():
    """
    Process file metadata and generate cleaned file names and target paths.
    This table contains the mapping between original and cleaned file names.
    """
    return (
        dlt.read_stream("raw_file_events")
        .filter(col("original_file_name").rlike(r"\.(csv|txt|xlsx|parquet)$"))  # Only process supported file types
        .withColumn("file_name_without_ext", regexp_replace(col("original_file_name"), r"\.[^.]+$", ""))
        .withColumn("cleaned_file_name", expr("clean_file_name(file_name_without_ext)"))
        .withColumn("folder_name", expr("extract_directory_name(cleaned_file_name)"))
        .withColumn("target_directory", concat(
            lit(destination_directory), 
            lit("/"), 
            col("entity"), 
            lit("/"), 
            col("folder_name"), 
            lit("/")
        ))
        .withColumn("target_path", concat(
            col("target_directory"),
            col("cleaned_file_name")
        ))
        .withColumn("processing_status", lit("pending"))
        .withColumn("processed_at", current_timestamp())
        .select(
            col("source_path"),
            col("original_file_name"),
            col("cleaned_file_name"),
            col("folder_name"),
            col("target_directory"),
            col("target_path"),
            col("file_size_bytes"),
            col("file_modification_time"),
            col("detected_at"),
            col("processed_at"),
            col("processing_status"),
            col("environment"),
            col("entity")
        )
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## File Processing Log Table

# COMMAND ----------

@dlt.table(
    name="file_processing_log",
    comment="Comprehensive log of all file processing operations with status and error details",
    table_properties={
        "quality": "silver",
        "pipelines.autoOptimize.managed": "true"
    }
)
@dlt.expect("valid_log_entry", "original_file_name IS NOT NULL")
def file_processing_log():
    """
    Create a comprehensive log of file processing operations.
    This table tracks the success/failure of file moves and any errors encountered.
    """
    return (
        dlt.read_stream("processed_file_metadata")
        .withColumn("operation_id", expr("uuid()"))
        .withColumn("operation_type", lit("file_move"))
        .withColumn("operation_timestamp", current_timestamp())
        .withColumn("success", lit(True))  # This will be updated by the actual file processing logic
        .withColumn("error_message", lit(None).cast(StringType()))
        .select(
            col("operation_id"),
            col("source_path"),
            col("target_path"),
            col("original_file_name"),
            col("cleaned_file_name"),
            col("folder_name"),
            col("operation_type"),
            col("operation_timestamp"),
            col("success"),
            col("error_message"),
            col("file_size_bytes"),
            col("environment"),
            col("entity")
        )
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## File Processing with Actual File Operations

# COMMAND ----------

@dlt.table(
    name="file_move_operations",
    comment="Table that triggers actual file move operations and tracks results",
    table_properties={
        "quality": "silver",
        "pipelines.autoOptimize.managed": "true"
    }
)
def file_move_operations():
    """
    This table processes the file metadata and performs actual file move operations.
    It uses a foreachBatch operation to handle the file system operations.
    """
    def process_file_batch(batch_df, batch_id):
        """Process each batch of files and perform file move operations"""
        from databricks.sdk.runtime import dbutils

        # Collect the batch data
        files_to_process = batch_df.collect()

        for file_row in files_to_process:
            try:
                source_path = file_row.source_path
                target_path = file_row.target_path
                target_directory = file_row.target_directory

                # Create target directory if it doesn't exist
                try:
                    dbutils.fs.mkdirs(target_directory)
                except Exception as e:
                    print(f"Directory creation warning for {target_directory}: {e}")

                # Move the file
                dbutils.fs.mv(source_path, target_path)

                # Update the processing status
                batch_df = batch_df.withColumn("processing_status",
                    when(col("source_path") == source_path, "completed")
                    .otherwise(col("processing_status"))
                ).withColumn("error_message",
                    when(col("source_path") == source_path, lit(None))
                    .otherwise(col("error_message"))
                ).withColumn("completed_at",
                    when(col("source_path") == source_path, current_timestamp())
                    .otherwise(col("completed_at"))
                )

                print(f"Successfully moved {file_row.original_file_name} to {target_path}")

            except Exception as e:
                error_msg = str(e)
                print(f"Error processing file {file_row.original_file_name}: {error_msg}")

                # Update with error status
                batch_df = batch_df.withColumn("processing_status",
                    when(col("source_path") == source_path, "failed")
                    .otherwise(col("processing_status"))
                ).withColumn("error_message",
                    when(col("source_path") == source_path, lit(error_msg))
                    .otherwise(col("error_message"))
                ).withColumn("completed_at",
                    when(col("source_path") == source_path, current_timestamp())
                    .otherwise(col("completed_at"))
                )

    return (
        dlt.read_stream("processed_file_metadata")
        .filter(col("processing_status") == "pending")
        .withColumn("completed_at", lit(None).cast(TimestampType()))
        .withColumn("error_message", lit(None).cast(StringType()))
        .writeStream
        .foreachBatch(process_file_batch)
        .outputMode("append")
        .trigger(processingTime="30 seconds")  # Process files every 30 seconds
        .option("checkpointLocation", f"{destination_directory}/_checkpoints/file_moves")
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Enhanced File Processing Log

# COMMAND ----------

@dlt.table(
    name="enhanced_file_processing_log",
    comment="Enhanced log table that combines metadata with actual processing results",
    table_properties={
        "quality": "silver",
        "pipelines.autoOptimize.managed": "true"
    }
)
@dlt.expect("valid_log_entry", "OldFileName IS NOT NULL")
@dlt.expect("valid_status", "Status IN ('pending', 'completed', 'failed')")
@dlt.expect("valid_environment", "Environment IN ('dev', 'prod')")
@dlt.expect("valid_entity", "Entity IS NOT NULL AND Entity != ''")
def enhanced_file_processing_log():
    """
    Enhanced logging table that provides complete audit trail of file processing.
    This table is compatible with the original notebook's logging structure.
    """
    return (
        dlt.read("processed_file_metadata")
        .select(
            col("original_file_name").alias("OldFileName"),
            col("cleaned_file_name").alias("CleanedFileName"),
            col("folder_name").alias("FolderName"),
            col("processed_at").alias("DateOperation"),
            col("processing_status").alias("Status"),
            col("error_message").alias("ErrorMessage"),
            col("source_path").alias("SourcePath"),
            col("target_path").alias("TargetPath"),
            col("file_size_bytes").alias("FileSizeBytes"),
            col("environment").alias("Environment"),
            col("entity").alias("Entity")
        )
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Summary Statistics Table

# COMMAND ----------

@dlt.table(
    name="file_processing_summary",
    comment="Daily summary statistics of file processing operations",
    table_properties={
        "quality": "gold",
        "pipelines.autoOptimize.managed": "true"
    }
)
def file_processing_summary():
    """
    Generate daily summary statistics of file processing operations.
    This provides insights into processing volumes and success rates.
    """
    return (
        dlt.read("enhanced_file_processing_log")
        .withColumn("processing_date", date_format(col("DateOperation"), "yyyy-MM-dd"))
        .groupBy("processing_date", "Environment", "Entity", "FolderName")
        .agg(
            count("*").alias("total_files_processed"),
            sum(when(col("Status") == "completed", 1).otherwise(0)).alias("successful_files"),
            sum(when(col("Status") == "failed", 1).otherwise(0)).alias("failed_files"),
            sum(when(col("Status") == "pending", 1).otherwise(0)).alias("pending_files"),
            sum("FileSizeBytes").alias("total_bytes_processed"),
            min("DateOperation").alias("first_file_processed_at"),
            max("DateOperation").alias("last_file_processed_at")
        )
        .withColumn("success_rate", round(col("successful_files") / col("total_files_processed") * 100, 2))
        .withColumn("summary_generated_at", current_timestamp())
    )
