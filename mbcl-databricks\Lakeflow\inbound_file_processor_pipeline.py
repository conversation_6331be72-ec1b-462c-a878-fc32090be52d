# Databricks notebook source
# MAGIC %md
# MAGIC # Inbound File Processing - Simple Lakeflow Pipeline
# MAGIC
# MAGIC Simple pipeline that processes inbound files using Auto Loader.
# MAGIC Applies the same file cleaning logic as the original Inbound_Files.py notebook.

# COMMAND ----------

import dlt
import re
from pyspark.sql.functions import *
from pyspark.sql.types import *

# COMMAND ----------

# Configuration
source_directory = "/Volumes/dbw_dev_itdev_test_uks/bronze_xva/inbound_files/"
destination_directory = "/Volumes/dbw_dev_itdev_test_uks/bronze_xva/inbound_cleanfiles/"
# Use a separate directory within the same volume for schemas
schema_location = "/Volumes/dbw_dev_itdev_test_uks/bronze_xva/pipeline_schemas/"

# Create schema directory if it doesn't exist
try:
    dbutils.fs.mkdirs(schema_location)
    print(f"✅ Schema directory ready: {schema_location}")
except Exception as e:
    print(f"⚠️ Schema directory creation: {e}")

# File cleaning patterns (same as original notebook)
date_pattern = re.compile(r'\d-\d-\dT\d:\d:\d')
non_alphanumeric_pattern = re.compile(r'[^a-zA-Z0-9]')
numeric_pattern = re.compile(r'\d')
non_alpha_pattern = re.compile(r'[^a-zA-Z]')

def clean_file_name(file_name):
    """Clean and standardize file names - same logic as original notebook"""
    first_part = file_name[:19]

    if date_pattern.match(first_part):
        file_name = file_name[20:]
        first_part = ''
    else:
        first_part = non_alphanumeric_pattern.sub('', file_name[:15])

    has_number = any(char.isdigit() for char in first_part)

    if not has_number:
        parts = file_name.split('_')
        if len(parts) > 2:
            parts[2] = numeric_pattern.sub('', parts[2])
            file_name = '_'.join(parts)
        else:
            file_name = '_'.join(parts)

    if has_number:
        first_part += '_'

    second_part = non_alpha_pattern.sub('', file_name)
    cleaned_name = second_part
    cleaned_file_name = cleaned_name + '.csv'

    return cleaned_file_name

def extract_directory_name(cleaned_file_name):
    """Extract directory name from cleaned file name"""
    if cleaned_file_name.endswith('.csv'):
        cleaned_file_name = cleaned_file_name[:-4]

    dir_name = numeric_pattern.sub('', cleaned_file_name)
    dir_name = dir_name.replace(' ', '')
    dir_name = non_alpha_pattern.sub('', dir_name)

    return dir_name.lower()

# Register UDFs
spark.udf.register("clean_file_name", clean_file_name, StringType())
spark.udf.register("extract_directory_name", extract_directory_name, StringType())

# COMMAND ----------

# Auto Loader table to detect new files
@dlt.table(
    name="raw_file_events",
    comment="Files detected by Auto Loader"
)
def raw_file_events():
    return (
        spark.readStream
        .format("cloudFiles")
        .option("cloudFiles.format", "binaryFile")
        .option("cloudFiles.useNotifications", "false")  # Disabled notifications
        .option("cloudFiles.includeExistingFiles", "true")  # Include existing files for testing
        .option("cloudFiles.schemaLocation", f"{schema_location}raw_file_events")
        .load(source_directory)
        .select(
            col("path").alias("source_path"),
            col("modificationTime").alias("file_modification_time"),
            col("length").alias("file_size_bytes"),
            regexp_extract(col("path"), r"([^/]+)$", 1).alias("original_file_name"),
            current_timestamp().alias("detected_at")
        )
    )

# COMMAND ----------

# Process files and apply cleaning logic
@dlt.table(
    name="processed_files",
    comment="Files with cleaned names and target paths"
)
@dlt.expect_or_fail("valid_file_name", "original_file_name IS NOT NULL")
@dlt.expect_or_fail("valid_cleaned_name", "cleaned_file_name IS NOT NULL")
def processed_files():
    return (
        dlt.read_stream("raw_file_events")
        .filter(col("original_file_name").rlike(r"\.(csv|txt|xlsx|parquet)$"))
        .withColumn("file_name_without_ext", regexp_replace(col("original_file_name"), r"\.[^.]+$", ""))
        .withColumn("cleaned_file_name", expr("clean_file_name(file_name_without_ext)"))
        .withColumn("folder_name", expr("extract_directory_name(cleaned_file_name)"))
        .withColumn("target_directory", concat(
            lit(destination_directory),
            lit("XVA/"),
            col("folder_name"),
            lit("/")
        ))
        .withColumn("target_path", concat(
            col("target_directory"),
            col("cleaned_file_name")
        ))
        .withColumn("processed_at", current_timestamp())
        .select(
            col("source_path"),
            col("original_file_name"),
            col("cleaned_file_name"),
            col("folder_name"),
            col("target_directory"),
            col("target_path"),
            col("file_size_bytes"),
            col("processed_at")
        )
    )

# COMMAND ----------

# Simple processing log (compatible with original notebook format)
@dlt.table(
    name="file_processing_log",
    comment="Simple log of file processing operations"
)
def file_processing_log():
    return (
        dlt.read("processed_files")
        .select(
            col("original_file_name").alias("OldFileName"),
            col("cleaned_file_name").alias("CleanedFileName"),
            col("folder_name").alias("FolderName"),
            col("processed_at").alias("DateOperation"),
            col("source_path").alias("SourcePath"),
            col("target_path").alias("TargetPath")
        )
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## File Move Operations
# MAGIC
# MAGIC This cell performs the actual file moves. Run this after the pipeline processes files.

# COMMAND ----------

def move_processed_files():
    """
    Function to actually move the files based on the processed_files table.
    This should be run as a separate job or manually after the pipeline runs.
    """
    from databricks.sdk.runtime import dbutils

    # Read the latest processed files that haven't been moved yet
    processed_df = spark.table("processed_files")

    # Get the files to process
    files_to_move = processed_df.collect()

    print(f"Found {len(files_to_move)} files to process")

    successful_moves = 0
    failed_moves = 0

    for file_row in files_to_move:
        try:
            source_path = file_row.source_path
            target_path = file_row.target_path
            target_directory = file_row.target_directory
            original_name = file_row.original_file_name
            cleaned_name = file_row.cleaned_file_name

            # Create target directory if it doesn't exist
            try:
                dbutils.fs.mkdirs(target_directory)
                print(f"✅ Created directory: {target_directory}")
            except Exception as e:
                print(f"⚠️ Directory creation warning for {target_directory}: {e}")

            # Check if source file exists
            try:
                dbutils.fs.ls(source_path)
            except Exception:
                print(f"❌ Source file not found: {source_path}")
                failed_moves += 1
                continue

            # Move the file
            dbutils.fs.mv(source_path, target_path)
            successful_moves += 1

            print(f"✅ Successfully moved: {original_name} → {cleaned_name}")
            print(f"   From: {source_path}")
            print(f"   To: {target_path}")

        except Exception as e:
            failed_moves += 1
            error_msg = str(e)
            print(f"❌ Error moving file {file_row.original_file_name}: {error_msg}")

    print(f"\n📊 Summary:")
    print(f"   ✅ Successful moves: {successful_moves}")
    print(f"   ❌ Failed moves: {failed_moves}")
    print(f"   📁 Total files processed: {len(files_to_move)}")

    return successful_moves, failed_moves

# Uncomment the line below to run the file moves manually
# move_processed_files()


