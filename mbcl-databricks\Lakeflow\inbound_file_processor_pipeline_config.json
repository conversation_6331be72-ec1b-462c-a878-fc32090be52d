{"name": "simple_inbound_file_processor", "storage": {"destination": "dbw_dev_itdev_test_uks.bronze_xva"}, "configuration": {"pipelines.trigger.interval": "5 minutes", "spark.databricks.delta.autoOptimize.optimizeWrite": "true", "spark.databricks.delta.autoOptimize.autoCompact": "true"}, "clusters": [{"label": "default", "autoscale": {"min_workers": 1, "max_workers": 3, "mode": "ENHANCED"}, "spark_version": "13.3.x-scala2.12", "custom_tags": {"Environment": "dev", "Project": "InboundFileProcessing"}}], "libraries": [], "development": true, "continuous": false, "photon": false, "serverless": true}