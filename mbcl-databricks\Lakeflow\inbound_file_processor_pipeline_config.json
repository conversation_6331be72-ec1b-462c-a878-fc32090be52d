{"name": "inbound_file_processor_pipeline", "storage": {"destination": "hive_metastore.mbcl_dev_bronze.inbound_files"}, "configuration": {"pipeline.env": "dev", "pipeline.entity": "XVA", "pipelines.trigger.interval": "5 minutes", "spark.databricks.delta.autoOptimize.optimizeWrite": "true", "spark.databricks.delta.autoOptimize.autoCompact": "true", "spark.sql.streaming.checkpointLocation.root": "/Volumes/mbcl_dev_bronze/xva/checkpoints/inbound_processor"}, "clusters": [{"label": "default", "policy_id": "your-policy-id-here", "autoscale": {"min_workers": 1, "max_workers": 5, "mode": "ENHANCED"}, "spark_version": "13.3.x-scala2.12", "spark_conf": {"spark.databricks.cluster.profile": "serverless", "spark.databricks.repl.allowedLanguages": "python,sql", "spark.databricks.delta.preview.enabled": "true"}, "custom_tags": {"Environment": "dev", "Project": "InboundFileProcessing", "Owner": "DataEngineering"}}], "libraries": [], "filters": {"notifications": {"alerts": ["on-update-failure", "on-update-fatal-failure"], "email_recipients": ["<EMAIL>"]}}, "development": true, "continuous": false, "photon": false, "serverless": true}