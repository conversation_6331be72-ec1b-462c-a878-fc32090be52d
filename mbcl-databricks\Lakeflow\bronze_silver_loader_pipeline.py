# Databricks notebook source
# MAGIC %md
# MAGIC # Bronze Silver Loader - Lakeflow Declarative Pipeline
# MAGIC 
# MAGIC This pipeline replaces the PC With FileLoader.py notebook with a modern Lakeflow approach.
# MAGIC It reads cleaned files and loads them into Bronze and Silver tables based on pipelinecontrol configuration.

# COMMAND ----------

import dlt
import re
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql import SparkSession
from delta.tables import DeltaTable

# COMMAND ----------

# Configuration - matches the original parameters
env = "dev"  # Can be parameterized via pipeline settings
entity = "XVA"  # Can be parameterized via pipeline settings

# Paths
cleaned_files_base = f"/Volumes/dbw_dev_itdev_test_uks/bronze_xva/inbound_cleanfiles/XVA/"
pipelinecontrol_table = "dbw_dev_itdev_test_uks.utility_metadata.pipelinecontrol"

print(f"🔧 Configuration: env={env}, entity={entity}")
print(f"📁 Source directory: {cleaned_files_base}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Pipeline Control Configuration

# COMMAND ----------

@dlt.table(
    name="active_pipeline_configs",
    comment="Active pipeline configurations from pipelinecontrol table"
)
def active_pipeline_configs():
    """
    Read active pipeline configurations for the specified entity.
    This replaces the manual config reading in the original notebook.
    """
    return (
        spark.table(pipelinecontrol_table)
        .filter(
            (col("Entity") == lit(entity)) & 
            (col("IsActive") == lit(True)) &
            (col("Source") == lit("File")) &
            (col("SourceType").isin(["csv", "parquet", "xlsx"]))
        )
        .select(
            col("EntityId"),
            col("Entity"),
            col("SourceTableName"),
            col("TargetTableName"),
            col("SchemaName"),
            col("RefinedSchemaName"),
            col("SourceType"),
            col("KeyColumn"),
            col("IsSilver"),
            col("last_update_column"),
            col("use_last_ts"),
            col("Input_FilePath"),
            col("Description")
        )
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## File Detection and Loading

# COMMAND ----------

@dlt.table(
    name="detected_cleaned_files",
    comment="Auto Loader detection of cleaned files ready for processing"
)
def detected_cleaned_files():
    """
    Detect cleaned files using Auto Loader.
    This replaces the manual file discovery in the original notebook.
    """
    return (
        spark.readStream
        .format("cloudFiles")
        .option("cloudFiles.format", "binaryFile")
        .option("cloudFiles.useNotifications", "false")
        .option("cloudFiles.includeExistingFiles", "true")
        .option("cloudFiles.schemaLocation", "/Volumes/dbw_dev_itdev_test_uks/bronze_xva/pipeline_schemas/cleaned_files")
        .load(cleaned_files_base)
        .select(
            col("path").alias("file_path"),
            col("modificationTime").alias("file_modification_time"),
            col("length").alias("file_size_bytes"),
            regexp_extract(col("path"), r"([^/]+)$", 1).alias("file_name"),
            regexp_extract(col("path"), r"/([^/]+)/[^/]+$", 1).alias("folder_name"),
            current_timestamp().alias("detected_at")
        )
        .filter(col("file_name").rlike(r"\.(csv|xlsx|parquet)$"))
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Bronze Layer - Raw Data Ingestion

# COMMAND ----------

def create_bronze_table(source_table_name: str, schema_name: str, source_type: str):
    """
    Dynamically create bronze tables for each source.
    This replaces the manual bronze table creation in the original notebook.
    """
    
    @dlt.table(
        name=f"bronze_{source_table_name.lower()}",
        comment=f"Bronze table for {source_table_name} - raw data from {source_type} files",
        table_properties={
            "quality": "bronze",
            "pipelines.autoOptimize.managed": "true"
        }
    )
    def bronze_table():
        # Get files for this specific source table
        files_df = dlt.read("detected_cleaned_files").filter(
            lower(col("folder_name")) == source_table_name.lower()
        )
        
        if source_type.lower() == "csv":
            # Read CSV files with Auto Loader
            return (
                spark.readStream
                .format("cloudFiles")
                .option("cloudFiles.format", "csv")
                .option("cloudFiles.useNotifications", "false")
                .option("cloudFiles.includeExistingFiles", "true")
                .option("cloudFiles.schemaLocation", f"/Volumes/dbw_dev_itdev_test_uks/bronze_xva/pipeline_schemas/bronze_{source_table_name.lower()}")
                .option("header", "true")
                .option("inferSchema", "true")
                .option("multiline", "true")
                .option("escape", '"')
                .load(f"{cleaned_files_base}{source_table_name.lower()}/")
                .withColumn("filename", input_file_name())
                .withColumn("ingestionTimestamp", current_timestamp())
                .withColumn("source_table", lit(source_table_name))
                .withColumn("entity", lit(entity))
                .withColumn("environment", lit(env))
            )
        
        elif source_type.lower() == "parquet":
            return (
                spark.readStream
                .format("cloudFiles")
                .option("cloudFiles.format", "parquet")
                .option("cloudFiles.useNotifications", "false")
                .option("cloudFiles.includeExistingFiles", "true")
                .option("cloudFiles.schemaLocation", f"/Volumes/dbw_dev_itdev_test_uks/bronze_xva/pipeline_schemas/bronze_{source_table_name.lower()}")
                .load(f"{cleaned_files_base}{source_table_name.lower()}/")
                .withColumn("filename", input_file_name())
                .withColumn("ingestionTimestamp", current_timestamp())
                .withColumn("source_table", lit(source_table_name))
                .withColumn("entity", lit(entity))
                .withColumn("environment", lit(env))
            )
        
        else:  # xlsx and other formats - will need batch processing
            # For Excel files, we'll need to handle them differently as streaming doesn't support Excel
            # This is a limitation we'll document
            return (
                spark.readStream
                .format("cloudFiles")
                .option("cloudFiles.format", "csv")  # Fallback to CSV for now
                .option("cloudFiles.useNotifications", "false")
                .option("cloudFiles.includeExistingFiles", "true")
                .option("cloudFiles.schemaLocation", f"/Volumes/dbw_dev_itdev_test_uks/bronze_xva/pipeline_schemas/bronze_{source_table_name.lower()}")
                .option("header", "true")
                .option("inferSchema", "true")
                .load(f"{cleaned_files_base}{source_table_name.lower()}/")
                .withColumn("filename", input_file_name())
                .withColumn("ingestionTimestamp", current_timestamp())
                .withColumn("source_table", lit(source_table_name))
                .withColumn("entity", lit(entity))
                .withColumn("environment", lit(env))
            )
    
    return bronze_table

# COMMAND ----------

# MAGIC %md
# MAGIC ## Silver Layer - Cleaned and Deduplicated Data

# COMMAND ----------

def create_silver_table(source_table_name: str, refined_schema_name: str, key_columns: str):
    """
    Create silver tables with deduplication and data quality.
    This replaces the silver table logic in the original notebook.
    """
    
    @dlt.table(
        name=f"silver_{source_table_name.lower()}",
        comment=f"Silver table for {source_table_name} - cleaned and deduplicated data",
        table_properties={
            "quality": "silver",
            "pipelines.autoOptimize.managed": "true"
        }
    )
    @dlt.expect_or_drop("valid_record", "source_table IS NOT NULL")
    @dlt.expect("data_quality", "ingestionTimestamp IS NOT NULL")
    def silver_table():
        bronze_df = dlt.read(f"bronze_{source_table_name.lower()}")
        
        # Clean column names (same logic as original)
        cleaned_df = bronze_df
        for col_name in bronze_df.columns:
            clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', col_name)
            if clean_name != col_name:
                cleaned_df = cleaned_df.withColumnRenamed(col_name, clean_name)
        
        # Apply deduplication if key columns are specified
        if key_columns and key_columns.strip():
            key_list = [k.strip() for k in key_columns.split(",")]
            # Filter out null key values and deduplicate
            for key in key_list:
                if key in cleaned_df.columns:
                    cleaned_df = cleaned_df.filter(col(key).isNotNull())
            
            # Deduplicate based on key columns
            cleaned_df = cleaned_df.dropDuplicates(key_list)
        
        return (
            cleaned_df
            .withColumn("silver_processed_at", current_timestamp())
            .withColumn("data_quality_score", lit(1.0))  # Can be enhanced with actual quality metrics
        )
    
    return silver_table

# COMMAND ----------

# MAGIC %md
# MAGIC ## Dynamic Table Creation

# COMMAND ----------

# Get pipeline configurations to create tables dynamically
def initialize_pipeline_tables():
    """
    Initialize bronze and silver tables based on pipeline configuration.
    This replaces the manual table creation in the original notebook.
    """
    try:
        # Read pipeline configurations
        configs = spark.table(pipelinecontrol_table).filter(
            (col("Entity") == lit(entity)) &
            (col("IsActive") == lit(True)) &
            (col("Source") == lit("File")) &
            (col("SourceType").isin(["csv", "parquet", "xlsx"]))
        ).collect()

        print(f"📋 Found {len(configs)} active pipeline configurations")

        # Create tables for each configuration
        for config in configs:
            source_table = config["SourceTableName"]
            schema_name = config["SchemaName"]
            refined_schema = config["RefinedSchemaName"]
            source_type = config["SourceType"]
            key_columns = config["KeyColumn"]
            is_silver = config["IsSilver"]

            print(f"🔄 Processing: {source_table} ({source_type})")

            # Create bronze table
            bronze_func = create_bronze_table(source_table, schema_name, source_type)

            # Create silver table if needed
            if is_silver:
                silver_func = create_silver_table(source_table, refined_schema, key_columns)
                print(f"✅ Created Bronze + Silver tables for {source_table}")
            else:
                print(f"✅ Created Bronze table for {source_table}")

    except Exception as e:
        print(f"❌ Error initializing pipeline tables: {str(e)}")
        raise

# Initialize tables when the pipeline starts
initialize_pipeline_tables()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Processing Log

# COMMAND ----------

@dlt.table(
    name="bronze_silver_processing_log",
    comment="Log of bronze and silver processing operations"
)
def bronze_silver_processing_log():
    """
    Create processing log similar to the original notebook's logging.
    """
    return (
        dlt.read("detected_cleaned_files")
        .select(
            col("file_name"),
            col("folder_name").alias("source_table"),
            col("file_path"),
            col("file_size_bytes"),
            col("detected_at"),
            lit("PROCESSED").alias("status"),
            lit(entity).alias("entity"),
            lit(env).alias("environment"),
            current_timestamp().alias("log_timestamp"),
            lit("Lakeflow pipeline processing").alias("message")
        )
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Pipeline Summary

# COMMAND ----------

@dlt.table(
    name="pipeline_summary",
    comment="Summary of pipeline execution and data quality metrics"
)
def pipeline_summary():
    """
    Create a summary table showing pipeline execution status.
    """
    return (
        dlt.read("bronze_silver_processing_log")
        .groupBy("source_table", "entity", "environment")
        .agg(
            count("*").alias("files_processed"),
            sum("file_size_bytes").alias("total_bytes_processed"),
            max("detected_at").alias("last_file_processed"),
            countDistinct("status").alias("unique_statuses")
        )
        .withColumn("summary_generated_at", current_timestamp())
        .withColumn("pipeline_type", lit("bronze_silver_loader"))
    )
