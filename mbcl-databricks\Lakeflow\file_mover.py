# Databricks notebook source
# MAGIC %md
# MAGIC # File Mover - Companion to Lakeflow Pipeline
# MAGIC 
# MAGIC This notebook performs the actual file moves based on the metadata created by the Lakeflow pipeline.
# MAGI<PERSON> Run this after the Lakeflow pipeline processes files, or schedule it to run periodically.

# COMMAND ----------

from databricks.sdk.runtime import dbutils
from pyspark.sql import SparkSession
from pyspark.sql.functions import *
from datetime import datetime

# COMMAND ----------

# Configuration - should match the Lakeflow pipeline
catalog_name = "dbw_dev_itdev_test_uks"
schema_name = "bronze_xva"
processed_files_table = f"{catalog_name}.{schema_name}.processed_files"

print(f"📋 Reading from table: {processed_files_table}")

# COMMAND ----------

def move_processed_files():
    """
    Function to actually move the files based on the processed_files table.
    This reads the metadata created by the Lakeflow pipeline and performs the physical file moves.
    """
    
    try:
        # Read the processed files table
        processed_df = spark.table(processed_files_table)
        
        # Get the files to process (you might want to add a filter for unprocessed files)
        files_to_move = processed_df.collect()
        
        print(f"📁 Found {len(files_to_move)} files to process")
        
        if len(files_to_move) == 0:
            print("ℹ️ No files to process")
            return 0, 0
        
        successful_moves = 0
        failed_moves = 0
        
        for file_row in files_to_move:
            try:
                source_path = file_row.source_path
                target_path = file_row.target_path
                target_directory = file_row.target_directory
                original_name = file_row.original_file_name
                cleaned_name = file_row.cleaned_file_name
                
                print(f"\n🔄 Processing: {original_name}")
                
                # Check if source file exists
                try:
                    source_files = dbutils.fs.ls(source_path)
                    print(f"   ✅ Source file found: {source_path}")
                except Exception:
                    print(f"   ⚠️ Source file not found (may have been processed already): {source_path}")
                    continue
                
                # Check if target file already exists
                try:
                    dbutils.fs.ls(target_path)
                    print(f"   ⚠️ Target file already exists, skipping: {target_path}")
                    continue
                except Exception:
                    # Target doesn't exist, which is good - we can proceed
                    pass
                
                # Create target directory if it doesn't exist
                try:
                    dbutils.fs.mkdirs(target_directory)
                    print(f"   📁 Ensured directory exists: {target_directory}")
                except Exception as e:
                    print(f"   ⚠️ Directory creation warning: {e}")
                
                # Move the file
                dbutils.fs.mv(source_path, target_path)
                successful_moves += 1
                
                print(f"   ✅ Successfully moved: {original_name} → {cleaned_name}")
                print(f"      From: {source_path}")
                print(f"      To: {target_path}")
                
            except Exception as e:
                failed_moves += 1
                error_msg = str(e)
                print(f"   ❌ Error moving file {file_row.original_file_name}: {error_msg}")
        
        print(f"\n📊 Final Summary:")
        print(f"   ✅ Successful moves: {successful_moves}")
        print(f"   ❌ Failed moves: {failed_moves}")
        print(f"   📁 Total files processed: {len(files_to_move)}")
        
        return successful_moves, failed_moves
        
    except Exception as e:
        print(f"❌ Error reading processed files table: {str(e)}")
        print(f"   Make sure the Lakeflow pipeline has run and created the table: {processed_files_table}")
        return 0, 0

# COMMAND ----------

# Run the file mover
print(f"🚀 Starting file move operations at {datetime.now()}")
print("=" * 60)

successful, failed = move_processed_files()

print("=" * 60)
print(f"🏁 File move operations completed at {datetime.now()}")

if successful > 0:
    print(f"✅ Successfully processed {successful} files")
if failed > 0:
    print(f"❌ Failed to process {failed} files")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Optional: Check Results
# MAGIC 
# MAGIC Run this cell to verify the files were moved correctly

# COMMAND ----------

def check_moved_files():
    """Check the destination directory to see moved files"""
    destination_base = "/Volumes/dbw_dev_itdev_test_uks/bronze_xva/inbound_cleanfiles/"
    
    try:
        # List the XVA directory
        xva_path = f"{destination_base}XVA/"
        print(f"📁 Checking destination directory: {xva_path}")
        
        try:
            folders = dbutils.fs.ls(xva_path)
            print(f"   Found {len(folders)} folders:")
            
            for folder in folders:
                folder_name = folder.name.rstrip('/')
                print(f"   📂 {folder_name}")
                
                # List files in each folder
                try:
                    files = dbutils.fs.ls(folder.path)
                    print(f"      📄 {len(files)} files:")
                    for file in files[:5]:  # Show first 5 files
                        print(f"         - {file.name}")
                    if len(files) > 5:
                        print(f"         ... and {len(files) - 5} more files")
                except Exception as e:
                    print(f"      ⚠️ Error listing files: {e}")
                    
        except Exception as e:
            print(f"   ⚠️ XVA directory not found or empty: {e}")
            
    except Exception as e:
        print(f"❌ Error checking destination: {e}")

# Uncomment to check results
# check_moved_files()
