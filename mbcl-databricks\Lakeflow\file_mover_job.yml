resources:
  jobs:
    File_Mover_Job:
      name: "File_Mover_Job_{{job.parameters.environment}}"
      description: "Job to move files based on Lakeflow pipeline metadata"
      email_notifications:
        on_failure:
          - <EMAIL>
          - balaji.<PERSON><PERSON><PERSON><PERSON>@mbcl.com
      schedule:
        # Run every 10 minutes to move files processed by Lakeflow
        quartz_cron_expression: "0 */10 * * * ?"
        timezone_id: UTC
        pause_status: UNPAUSED
      max_concurrent_runs: 1
      parameters:
        - name: environment
          default: dev
        - name: catalog_name
          default: dbw_dev_itdev_test_uks
        - name: schema_name
          default: bronze_xva
      tasks:
        - task_key: Move_Files
          notebook_task:
            notebook_path: /Workspace/Shared/mbcl-databricks/Lakeflow/file_mover
            base_parameters:
              catalog_name: "{{job.parameters.catalog_name}}"
              schema_name: "{{job.parameters.schema_name}}"
          job_cluster_key: file_mover_cluster
          timeout_seconds: 1800  # 30 minutes timeout
          retry_on_timeout: true
          max_retries: 2
          min_retry_interval_millis: 30000  # 30 seconds
      job_clusters:
        - job_cluster_key: file_mover_cluster
          new_cluster:
            spark_version: "13.3.x-scala2.12"
            node_type_id: "Standard_DS3_v2"  # Small cluster for file operations
            num_workers: 1  # Single worker is sufficient for file moves
            spark_conf:
              spark.databricks.delta.preview.enabled: "true"
            custom_tags:
              Environment: "{{job.parameters.environment}}"
              Project: InboundFileProcessing
              Owner: DataEngineering
              Purpose: FileMover
      tags:
        Environment: "{{job.parameters.environment}}"
        Project: InboundFileProcessing
        Owner: DataEngineering
        Pipeline: Lakeflow
        Component: FileMover
