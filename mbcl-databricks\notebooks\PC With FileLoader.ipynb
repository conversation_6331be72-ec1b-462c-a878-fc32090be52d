# Query the scope of the current environment
workspace_name = spark.conf.get("spark.databricks.workspaceUrl")

# Query the scope of Azure Key Vault and secrets
key_vault_scope = "akv_dataplatform"

# dbutils.secrets.get(scope="akv_dataplatform", key="test")
oracle_username = dbutils.secrets.get(scope="akv_dataplatform", key="oracle-mos-username")
oracle_password = dbutils.secrets.get(scope="akv_dataplatform", key="oracle-mos-password")


# --- Widgets ---
dbutils.widgets.text("env", "")
dbutils.widgets.text("entity", "")

env = dbutils.widgets.get("env").lower()
entity = dbutils.widgets.get("entity").upper()

# ========================== ingestion_job.py =============================
"""
Parallel Bronze → Silver ingestion for Databricks
-------------------------------------------------
* Uses Databricks widgets for parameter input
* Loads only the newest dated folder for file feeds
* Records progress in load_log_table
"""

from __future__ import annotations
import os, re, time, logging
from dataclasses import dataclass
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Optional, Tuple

import pandas as pd, chardet
from pyspark.sql.types import *
from pyspark.sql import SparkSession, DataFrame
from pyspark.sql.functions import col, lit, current_timestamp
from pyspark.sql.utils import AnalysisException
from delta.tables import DeltaTable
from delta.exceptions import ConcurrentWriteException

# ───────────────────────── CONFIG ─────────────────────────
@dataclass
class Config:
    env: str
    entity: str
    jdbc_url: str
    jdbc_user: str = oracle_username      # set via secret / widget
    jdbc_pwd:  str = oracle_password      # set via secret / widget
    max_threads: int = 7
    max_retries: int = 3
    backoff_sec: float = 2.0

    @property
    def jdbc_opts(self):
        return {
            "url":       self.jdbc_url,
            "user":      self.jdbc_user,
            "password":  self.jdbc_pwd,
            "driver":    "oracle.jdbc.OracleDriver",
            "fetchsize": "1000",
            "oracle.net.CONNECT_TIMEOUT": "10000",
            "oracle.net.READ_TIMEOUT":    "10000"
        }

    @property
    def ctl_tbl (self): return f"mbcl_{self.env}_utility.metadata.pipelinecontrol"
    @property
    def audit_tbl(self): return f"mbcl_{self.env}_utility.metadata.metadata_pipeline_audit"
    @property
    def log_tbl (self): return f"mbcl_{self.env}_utility.metadata.load_log_table"


def get_config(env: str, entity: str) -> Config:
    url = ("*********************************************"
           if env == "prod"
           else "**************************************************")
    return Config(env=env, entity=entity, jdbc_url=url)

# ───────────────────────── LOGGING ────────────────────────
_log = logging.getLogger("ingest")
if not _log.handlers:
    h = logging.StreamHandler()
    h.setFormatter(logging.Formatter("%(asctime)s [%(levelname)s] %(message)s",
                                     "%Y-%m-%d %H:%M:%S"))
    _log.addHandler(h)
    _log.setLevel(logging.INFO)

# ──────────────────── CASE/QUOTE HELPERS ──────────────────
# Updated delimiters: split on whitespace, hyphens, slashes, etc.
_DELIMS = re.compile(r"[ \t\r\n\f\v\-_/()]+")
_UPPER  = re.compile(r"^[A-Z0-9_]+$")

def custom_cap(word: str) -> str:
    if _UPPER.match(word):
        return word
    return word[0].upper() + word[1:] if len(word) > 1 else word.upper()

def to_camel(s: str) -> str:
    """Convert column names to camelCase and replace special characters"""
    s = s.strip()
    # Replace common special characters with safe equivalents
    s = s.replace('%', 'Pct').replace('&', 'n')
    if not _DELIMS.search(s) and not _UPPER.match(s):
        return s[0].lower() + s[1:]
    parts = [p for p in _DELIMS.split(s) if p]
    column_name = parts[0].lower() + ''.join(custom_cap(p) for p in parts[1:])
    return column_name

def clean_cols(df: DataFrame) -> DataFrame:
    """Convert all column names to camelCase and log renames"""
    seen = {}
    for c in df.columns:
        new = to_camel(c)
        if new not in seen:
            df = df.withColumnRenamed(c, new)
            seen[new] = c
        else:
            _log.warning("Duplicate column '%s' → '%s' dropped", c, new)
            df = df.drop(c)
    _log.info("Column renaming map: %s", seen)
    return df

_QUOTE = re.compile(r"[^A-Z0-9_]")
def q(id_: str) -> str:
    return f'"{id_}"' if _QUOTE.search(id_) else id_

def table_exists(spark: SparkSession, ident: str) -> bool:
    try:
        spark.table(ident).limit(0).collect()
        return True
    except AnalysisException:
        return False

def safe_delete(path: str):
    try:
        dbutils.fs.ls(path)
        dbutils.fs.rm(path, recurse=True)
        _log.info(f"Deleted '{path}'")
    except Exception:
        _log.info(f"Path '{path}' does not exist")

def archive_file(base: str):
    # After the ingestion, we need to move the file to archive and delete the directory

    # Create archive directory if not exists
    archive_directory = f"/Volumes/mbcl_{env}_bronze/{entity.lower()}/inbound_archive/"
    if not dbutils.fs.mkdirs(archive_directory):
        _log.error(f"Failed to create archive directory: {archive_directory}") 
    
    files = dbutils.fs.ls(f"{base}")
    for file in files:
        folder_name = file.path.split("/")[-2]
   
        current_date = datetime.now().strftime('%Y/%m/%d/%H/%M')
        archive_path_for_file = f"{archive_directory}/{entity}/{folder_name}/{current_date}/"
        
        # if directory exists, use an unique identifier
        try:
            dbutils.fs.ls(archive_path_for_file)
            unique_id = datetime.now().strftime('%Y%m%d%H%M%S')
            archive_path_for_file = f"{archive_directory}/{entity}/{folder_name}/{current_date}_{unique_id}/"
        except:
            pass

        archive_path = f"{archive_path_for_file}{file.name}" 
        file_to_be_archived = os.path.join(base, file.name) 
        try: 
            dbutils.fs.mv(file_to_be_archived, archive_path, recurse=True)
            _log.info(f"Archived file: {file.name} to path: {archive_path}")
        except Exception as e:
            _log.error(f"Unable to archive: {file.name} to path: {archive_path}")
              
    # remove the directory after archinving the files
    safe_delete(base)
# ──────────────────── ENTRY DESCRIPTION ──────────────────
@dataclass
class Entry:
    src_tbl: str
    schema: str
    ref_schema: str
    tgt_tbl: str
    ts_raw: str
    key_raw: List[str]
    use_ts: Optional[bool]
    is_silver: bool
    src_type: str
    file_root: str
    ora_schema: str

    @property
    def bronze(self): return f"{self.schema}.{self.tgt_tbl}"
    @property
    def silver(self): return f"{self.ref_schema}.{self.tgt_tbl}"
    @property
    def ts_norm(self): return to_camel(self.ts_raw)
    @property
    def key_norm(self): return [to_camel(k) for k in self.key_raw]

# ───────────────── AUDIT / LOAD LOG ───────────────────────
def audit(spark, cfg, src, field, old, new):
    if str(old) != str(new):
        spark.createDataFrame(
            [(src, field, str(old), str(new), datetime.now())],
            "src STRING, field STRING, old STRING, new STRING, ts TIMESTAMP"
        ).write.mode("append").saveAsTable(cfg.audit_tbl)

def log_load(spark, cfg, entry, rows, status, msg):
    spark.createDataFrame(
        [(cfg.entity, entry.src_tbl, entry.bronze, entry.silver,
          status, rows, datetime.now(), msg)],
        """entity STRING, source_table STRING, bronze_table STRING, silver_table STRING,
           status STRING, rows_ingested INT, ts TIMESTAMP, message STRING"""
    ).write.mode("append").option("mergeSchema", "true").saveAsTable(cfg.log_tbl)
    _log.info("%s | %s rows | %s | %s", entry.src_tbl, rows, status, msg)

# ──────────────────── FILE INGEST HELPERS ─────────────────
def resolve_files(entry: Entry, cfg: Config) -> Tuple[str, List[str]]:
    base = os.path.join(entry.file_root.format(env=cfg.env), cfg.entity, entry.src_tbl)
    if not os.path.isdir(base):
        raise FileNotFoundError(base)
    files =[f for f in dbutils.fs.ls(f"{base}") if not f.isDir()]
    latest_file = sorted(files, key=lambda x:x.modificationTime, reverse=True)[0]
    latest_file = [os.path.join(base, latest_file.name)]
    return base, latest_file

def load_file(p: str, typ: str):
    try:
        if typ == "csv":
            # Detect encoding using chardet
            with open(p, "rb") as h:
                raw = h.read(1_000_000)
                enc = chardet.detect(raw).get("encoding") or "utf-8"
                if enc.lower() in {"ascii", "unknown", "none"}:
                    enc = "utf-8"
            try:
                df = pd.read_csv(p, encoding=enc, on_bad_lines="skip", dtype=str, na_values=["", "null", "NaN"])
            except UnicodeDecodeError as e:
                # Fallback encodings if utf-8 fails
                _log.warning("Encoding '%s' failed for %s, retrying with latin1", enc, p)
                try:
                    df = pd.read_csv(p, encoding="latin1", on_bad_lines="skip")
                except Exception as e2:
                    return None, (p, f"Encoding fallback failed: {str(e2)}")

        elif typ == "parquet":
            df = pd.read_parquet(p)

        elif typ == "xlsx":
            df = pd.read_excel(p)

        else:
            return None, (p, f"Unsupported file type: {typ}")

        df["filename"] = os.path.basename(p)
        return df, None

    except Exception as e:
        return None, (p, str(e))

def load_raw_batch(spark, cfg, entry):
    try:
        base, paths = resolve_files(entry, cfg)
    except Exception as e:
        return None, [(entry.file_root, str(e))], None
    dfs, errs = [], []
    with ThreadPoolExecutor(max_workers=4) as ex:
        futs = {ex.submit(load_file, p, entry.src_type): p for p in paths}
        for f in as_completed(futs):
            d, e = f.result()
            (dfs.append(d) if d is not None else errs.append(e))
    if not dfs:
        return None, errs, base
    if len(dfs) == 0:
        return None, [("No data files found", "Empty DataFrame list")], base
    if all(df.empty for df in dfs):
        return None, [("All data files are empty", "Empty DataFrame list")], base

    res = pd.concat(dfs, ignore_index=True)
    spark_df = spark.createDataFrame(res)
    for column in spark_df.schema.fields:
        if column.dataType.typeName() == "void":
            spark_df = spark_df.withColumn(column.name, col(column.name).cast(StringType()))
    return spark_df, errs, base


# ─────────────────── DELTA HELPERS ────────────────────────
def align_schema(schema, incoming):
    have = set(incoming.columns)
    for f in schema:
        if f.name not in have:
            incoming = incoming.withColumn(f.name, lit(None).cast(f.dataType))
    return incoming

def delta_retry(df, mode, table, cfg):
    for i in range(cfg.max_retries + 1):
        try:
            (df.write.format("delta")
               .mode(mode).saveAsTable(table))
            return
        except Exception as e:
            error_str = str(e)
            if "ConcurrentWriteException" in error_str or "ConcurrentAppendException" in error_str:
                if i == cfg.max_retries:
                    raise
                wait = cfg.backoff_sec * (2 ** i)
                _log.warning("delta clash %s – %.1fs", table, wait)
                time.sleep(wait)
   
# ───────────────── TIMESTAMP RESOLVER ─────────────────────
def get_timestamp_column(schema, entry: Entry) -> Optional[str]:
    cols = [f.name for f in schema]
    if entry.ts_raw  in cols: return entry.ts_raw
    if entry.ts_norm in cols: return entry.ts_norm
    _log.warning("TS column '%s' absent in %s", entry.ts_raw, cols)
    return None

# ────────────────── WORKER FUNCTION ───────────────────────
def worker(entry: Entry, cfg: Config, cache: Dict[str, bool]):
    spark = SparkSession.builder.getOrCreate()
    msg_path = ""

    # Determine timestamp filtering on first run
    if entry.use_ts is None:
        cols = (spark.read.format("jdbc").options(**cfg.jdbc_opts)
                .option("dbtable",
                        f"(SELECT * FROM {entry.ora_schema}.{entry.src_tbl} WHERE ROWNUM=1) t")
                ).load().columns
        entry.use_ts = any(c.upper() == entry.ts_raw.upper() for c in cols)
        audit(spark, cfg, entry.src_tbl, "use_last_ts", None, entry.use_ts)
        spark.sql(
            f"""
            UPDATE {cfg.ctl_tbl}
               SET use_last_ts={str(entry.use_ts).lower()}
             WHERE SourceTableName='{entry.src_tbl}'
               AND entity='{cfg.entity}'
            """
        )

    bronze_exists = cache.get(entry.bronze) or table_exists(spark, entry.bronze)
    bronze_schema = spark.table(entry.bronze).schema if bronze_exists else None
   
    if bronze_exists:
        cache[entry.bronze] = True

    # Build WHERE clause if timestamp filtering is enabled
    where = ""
    if entry.use_ts and bronze_schema:
        tscol = get_timestamp_column(bronze_schema, entry)
        if tscol:
            default_start = datetime(1970, 1, 1)
            last_val = (spark.table(entry.bronze)
                        .agg({tscol: "max"})
                        .collect()[0][0] or default_start)
            where = (
                "WHERE {col} > TO_TIMESTAMP('{val}', 'YYYY-MM-DD HH24:MI:SS.FF')"
                .format(col=q(entry.ts_raw),
                        val=last_val.strftime("%Y-%m-%d %H:%M:%S.%f")[:-3])
            )

    # Load data
    if entry.src_type in {"csv", "parquet", "xlsx"}:
        df, errs, base = load_raw_batch(spark, cfg, entry)
        msg_path = base or ""
        _log.info("[%s] folder %s", entry.src_tbl, base)
        if df is None:
            if errs == [('All data files are empty', 'Empty DataFrame list')]:
                archive_file(base)
            log_load(spark, cfg, entry, 0, "FAILED", f"{base} | {errs}")
            return
    else:
        df = (spark.read.format("jdbc").options(**cfg.jdbc_opts)
              .option("dbtable",
                      f"(SELECT * FROM {entry.ora_schema}.{entry.src_tbl} {where}) t")
              ).load()
        _log.info("[%s] Oracle view %s.%s", entry.src_tbl,
                  entry.ora_schema, entry.src_tbl)
    
    # Clean/align
    df = clean_cols(df).withColumn("ingestionTimestamp", current_timestamp())
    if bronze_schema:
        df = align_schema(bronze_schema, df)

    # Write bronze
    rows = df.count()
    write_mode = "overwrite" if entry.src_type in {"csv", "parquet", "xlsx"} else "append"
    delta_retry(df, write_mode, entry.bronze, cfg)
    cache[entry.bronze] = True

    
    # Write silver
    if entry.is_silver:
        ds = df
        for k in entry.key_norm:
            ds = ds.filter(col(k).isNotNull())
        ds = ds.dropDuplicates(entry.key_norm)

        if table_exists(spark, entry.silver):
            cond = " AND ".join([f"target.{k}=source.{k}" for k in entry.key_norm])
            for i in range(cfg.max_retries + 1):
                try:
                    (DeltaTable.forName(spark, entry.silver).alias("target")
                     .merge(ds.alias("source"), cond)
                     .whenMatchedUpdateAll()
                     .whenNotMatchedInsertAll()
                     .execute())
                    break
                except ConcurrentWriteException:
                    if i == cfg.max_retries:
                        raise
                    wait = cfg.backoff_sec * (2 ** i)
                    _log.warning("merge clash %s – %.1fs", entry.silver, wait)
                    time.sleep(wait)
        else:
            delta_retry(ds, "overwrite", entry.silver, cfg)

    # archive file after ingestion
    archive_file(base)
        
    log_load(spark, cfg, entry, rows, "SUCCESS", msg_path)

# ───────────────────── MAIN DRIVER ────────────────────────
def main(cfg: Config):
    spark = SparkSession.getActiveSession()
    rows = (spark.table(cfg.ctl_tbl)
            .filter((col("entity") == cfg.entity) & (col("IsActive") == True))
            .collect())

    entries = [Entry(
        src_tbl    = r["SourceTableName"],
        schema     = r["SchemaName"],
        ref_schema = r["RefinedSchemaName"],
        tgt_tbl    = r["TargetTableName"],
        ts_raw     = r["last_update_column"],
        key_raw    = r["KeyColumn"] if r["KeyColumn"] is None else r["KeyColumn"].split(","),
        use_ts     = r["use_last_ts"],
        is_silver  = r["IsSilver"],
        src_type   = r["SourceType"].lower(),
        file_root  = r["Input_FilePath"],
        ora_schema = r["oracle_schema"]
    ) for r in rows]

    if not entries:
        _log.info("nothing to ingest")
        return

    n_threads = max(1, min(len(entries), cfg.max_threads))
    _log.info("processing %d tables with %d threads", len(entries), n_threads)
    cache: Dict[str, bool] = {}

    with ThreadPoolExecutor(max_workers=n_threads) as pool:
        futs = [pool.submit(worker, e, cfg, cache) for e in entries]
        for f in as_completed(futs):
            f.result()       # re-raise exceptions

    _log.info("all tables processed")

# ──────────────────── ENTRYPOINT ──────────────────────────
if __name__ == "__main__":
    if not env or not entity:
        raise ValueError("Both 'env' and 'entity' parameters must be provided")

    _log.info("Starting ingestion job for env=%s, entity=%s", env, entity)
    try:
        cfg = get_config(env, entity)
        main(cfg)
        _log.info("Ingestion job completed successfully")
    except Exception as exc:
        _log.error("Job failed: %s", exc)
        raise