resources:
  jobs:
    Inbound_File_Processor_Lakeflow:
      name: "Inbound_File_Processor_Lakeflow_{{job.parameters.environment}}"
      description: "Lakeflow Declarative Pipeline for processing inbound files with Auto Loader in triggered mode"
      email_notifications:
        on_success:
          - <EMAIL>
        on_failure:
          - <EMAIL>
          - balaji.<PERSON><PERSON><PERSON><PERSON>@mbcl.com
      schedule:
        # Run every 15 minutes to check for new files
        quartz_cron_expression: "0 */15 * * * ?"
        timezone_id: UTC
        pause_status: UNPAUSED
      max_concurrent_runs: 1
      parameters:
        - name: environment
          default: dev
        - name: entity
          default: XVA
        - name: catalog_name
          default: mbcl_dev_bronze
        - name: schema_name
          default: inbound_files
      tasks:
        - task_key: Run_Inbound_File_Pipeline
          pipeline_task:
            pipeline_id: "{{pipeline.inbound_file_processor_pipeline.id}}"
            full_refresh: false
          timeout_seconds: 3600  # 1 hour timeout
          retry_on_timeout: true
          max_retries: 2
          min_retry_interval_millis: 60000  # 1 minute
          condition_task:
            op: EQUAL_TO
            left: "{{job.parameters.environment}}"
            right: "{{job.parameters.environment}}"
      tags:
        Environment: "{{job.parameters.environment}}"
        Project: InboundFileProcessing
        Owner: DataEngineering
        Pipeline: Lakeflow
      queue:
        enabled: true

  pipelines:
    inbound_file_processor_pipeline:
      name: "inbound_file_processor_pipeline_{{pipeline.parameters.environment}}"
      target: "{{pipeline.parameters.catalog_name}}.{{pipeline.parameters.schema_name}}"
      libraries:
        - notebook:
            path: /Workspace/Shared/mbcl-databricks/Lakeflow/inbound_file_processor_pipeline
      configuration:
        pipeline.env: "{{pipeline.parameters.environment}}"
        pipeline.entity: "{{pipeline.parameters.entity}}"
        pipelines.trigger.interval: "5 minutes"
        spark.databricks.delta.autoOptimize.optimizeWrite: "true"
        spark.databricks.delta.autoOptimize.autoCompact: "true"
        spark.sql.streaming.checkpointLocation.root: "/Volumes/{{pipeline.parameters.catalog_name}}/{{pipeline.parameters.entity | lower}}/checkpoints/inbound_processor"
      parameters:
        - name: environment
          default: dev
        - name: entity
          default: XVA
        - name: catalog_name
          default: mbcl_dev_bronze
        - name: schema_name
          default: inbound_files
      clusters:
        - label: default
          autoscale:
            min_workers: 1
            max_workers: 5
            mode: ENHANCED
          spark_version: "13.3.x-scala2.12"
          spark_conf:
            spark.databricks.cluster.profile: serverless
            spark.databricks.repl.allowedLanguages: python,sql
            spark.databricks.delta.preview.enabled: "true"
            spark.databricks.delta.autoOptimize.optimizeWrite: "true"
            spark.databricks.delta.autoOptimize.autoCompact: "true"
          custom_tags:
            Environment: "{{pipeline.parameters.environment}}"
            Project: InboundFileProcessing
            Owner: DataEngineering
      notifications:
        alerts:
          - "on-update-failure"
          - "on-update-fatal-failure"
        email_recipients:
          - "<EMAIL>"
      development: false
      continuous: false  # Triggered mode
      photon: false
      serverless: true
      edition: advanced
