# Databricks notebook source
# MAGIC %md
# MAGIC # PC FileLoader - Dynamic Lakeflow Pipeline
# MAGIC 
# MAGIC This pipeline replaces PC With FileLoader.py using Lakeflow declarative approach.
# MAGIC It dynamically reads from pipelinecontrol table and creates Bronze/Silver tables accordingly.
# MAGIC 
# MAGIC **Parameters:**
# MAGIC - entity: XVA (configurable)
# MAGIC - env: dev (configurable)

# COMMAND ----------

import dlt
import re
from pyspark.sql.functions import *
from pyspark.sql.types import *
from pyspark.sql.window import Window

# COMMAND ----------

# MAGIC %md
# MAGIC ## Configuration

# COMMAND ----------

# Pipeline parameters - can be set via pipeline configuration
entity = spark.conf.get("entity", "XVA")
env = spark.conf.get("env", "dev")

# Paths
cleaned_files_base = f"/Volumes/dbw_dev_itdev_test_uks/bronze_xva/inbound_cleanfiles/{entity}/"
pipelinecontrol_table = "dbw_dev_itdev_test_uks.utility_metadata.pipelinecontrol"
schema_location_base = "/Volumes/dbw_dev_itdev_test_uks/bronze_xva/pipeline_schemas/"

print(f"🔧 Entity: {entity}, Environment: {env}")
print(f"📁 Source: {cleaned_files_base}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Pipeline Configuration Source

# COMMAND ----------

@dlt.table(
    name="active_pipeline_configs",
    comment="Active pipeline configurations from pipelinecontrol table"
)
def active_pipeline_configs():
    """
    Read active pipeline configurations for the specified entity.
    This drives the dynamic table creation.
    """
    return (
        spark.table(pipelinecontrol_table)
        .filter(
            (col("Entity") == entity) & 
            (col("IsActive") == True) &
            (col("Source") == "File") &
            (col("SourceType").isin(["csv", "parquet", "xlsx"]))
        )
        .select(
            col("EntityId"),
            col("Entity"),
            col("SourceTableName"),
            col("TargetTableName"),
            col("SchemaName"),
            col("RefinedSchemaName"),
            col("SourceType"),
            col("KeyColumn"),
            col("IsSilver"),
            col("Description"),
            col("Input_FilePath")
        )
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## File Detection

# COMMAND ----------

@dlt.table(
    name="detected_files",
    comment="Files detected in cleaned files directory"
)
def detected_files():
    """
    Detect all files in the cleaned files directory using Auto Loader.
    This provides the source for all bronze tables.
    """
    return (
        spark.readStream
        .format("cloudFiles")
        .option("cloudFiles.format", "binaryFile")
        .option("cloudFiles.useNotifications", "false")
        .option("cloudFiles.includeExistingFiles", "true")
        .option("cloudFiles.schemaLocation", f"{schema_location_base}detected_files")
        .load(cleaned_files_base)
        .select(
            col("path").alias("file_path"),
            col("modificationTime").alias("file_modification_time"),
            col("length").alias("file_size_bytes"),
            regexp_extract(col("path"), r"([^/]+)$", 1).alias("file_name"),
            regexp_extract(col("path"), r"/([^/]+)/[^/]+$", 1).alias("folder_name"),
            regexp_extract(col("path"), r"\.([^.]+)$", 1).alias("file_extension"),
            current_timestamp().alias("detected_at")
        )
        .filter(col("file_name").rlike(r"\.(csv|xlsx|parquet)$"))
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Universal Bronze Table

# COMMAND ----------

@dlt.table(
    name="bronze_files_raw",
    comment="Universal bronze table for all file types - raw data with metadata",
    table_properties={
        "quality": "bronze",
        "pipelines.autoOptimize.managed": "true"
    }
)
def bronze_files_raw():
    """
    Universal bronze table that reads CSV files from all folders.
    Matches original PC FileLoader logic for file processing.
    Note: Excel and Parquet files need separate handling in streaming context.
    """
    return (
        spark.readStream
        .format("cloudFiles")
        .option("cloudFiles.format", "csv")
        .option("cloudFiles.useNotifications", "false")
        .option("cloudFiles.includeExistingFiles", "true")
        .option("cloudFiles.schemaLocation", f"{schema_location_base}bronze_universal")
        .option("header", "true")
        .option("inferSchema", "true")
        .option("multiline", "true")
        .option("escape", '"')
        .option("recursiveFileLookup", "true")
        # Add encoding options to handle different file encodings (matching original logic)
        .option("encoding", "UTF-8")
        .option("ignoreCorruptFiles", "true")
        .option("mode", "PERMISSIVE")
        .load(cleaned_files_base)
        .withColumn("filename", col("_metadata.file_path"))
        .withColumn("source_folder", regexp_extract(col("_metadata.file_path"), r"/([^/]+)/[^/]+$", 1))
        .withColumn("file_modification_time", col("_metadata.file_modification_time"))
        .withColumn("ingestionTimestamp", current_timestamp())
        .withColumn("source_entity", lit(entity))
        .withColumn("environment", lit(env))
        .withColumn("file_type", lit("csv"))
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Latest File Selection (Matching Original Logic)

# COMMAND ----------

@dlt.table(
    name="latest_files_per_source",
    comment="Latest file per source folder - matches original PC FileLoader latest file selection"
)
def latest_files_per_source():
    """
    Select the latest file per source folder, matching the original PC FileLoader logic:
    files = sorted(files, key=lambda x:x.modificationTime, reverse=True)[0]
    """
    from pyspark.sql.window import Window

    window_spec = Window.partitionBy("source_folder").orderBy(col("file_modification_time").desc())

    return (
        dlt.read("bronze_files_raw")
        .withColumn("row_number", row_number().over(window_spec))
        .filter(col("row_number") == 1)
        .drop("row_number")
        .withColumn("is_latest_file", lit(True))
        .withColumn("latest_file_selected_at", current_timestamp())
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Dynamic Bronze Views

# COMMAND ----------

# Helper function to clean column names
def clean_column_names_sql(table_name: str) -> str:
    """Generate SQL to clean column names"""
    return f"""
    SELECT *,
           '{table_name}' as source_table_name
    FROM bronze_files_raw 
    WHERE lower(source_folder) = lower('{table_name}')
    """

# COMMAND ----------

# MAGIC %md
# MAGIC ## Processing Metrics

# COMMAND ----------

@dlt.table(
    name="processing_summary",
    comment="Summary of files processed by source table"
)
def processing_summary():
    """
    Create processing summary showing files processed per source table.
    This replaces individual table metrics.
    """
    return (
        dlt.read("bronze_files_raw")
        .groupBy("source_folder", "source_entity", "environment")
        .agg(
            count("*").alias("total_records"),
            countDistinct("filename").alias("unique_files"),
            max("ingestionTimestamp").alias("last_processed"),
            min("ingestionTimestamp").alias("first_processed")
        )
        .withColumn("processing_date", current_date())
        .withColumn("pipeline_run_id", lit(spark.conf.get("spark.databricks.clusterUsageTags.runId", "unknown")))
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Data Quality Monitoring

# COMMAND ----------

@dlt.table(
    name="data_quality_checks",
    comment="Data quality checks across all source tables"
)
@dlt.expect_or_drop("valid_filename", "filename IS NOT NULL")
@dlt.expect_or_drop("valid_folder", "source_folder IS NOT NULL")
@dlt.expect("data_freshness", "ingestionTimestamp > current_timestamp() - INTERVAL 7 DAYS")
def data_quality_checks():
    """
    Perform data quality checks on the universal bronze table.
    """
    return (
        dlt.read("bronze_files_raw")
        .withColumn("record_count", lit(1))
        .withColumn("has_null_filename", when(col("filename").isNull(), 1).otherwise(0))
        .withColumn("has_null_folder", when(col("source_folder").isNull(), 1).otherwise(0))
        .withColumn("quality_score", 
                   when((col("filename").isNotNull()) & (col("source_folder").isNotNull()), 1.0)
                   .otherwise(0.0))
        .withColumn("check_timestamp", current_timestamp())
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Column Cleaning Functions (Matching Original Logic)

# COMMAND ----------

# Replicate the original PC FileLoader column cleaning logic
import re

# Original regex patterns from PC FileLoader
_DELIMS = re.compile(r"[ \t\r\n\f\v\-_/()]+")
_UPPER = re.compile(r"^[A-Z0-9_]+$")

def custom_cap(word: str) -> str:
    """Custom capitalization matching original logic"""
    if _UPPER.match(word):
        return word
    return word[0].upper() + word[1:] if len(word) > 1 else word.upper()

def to_camel_case(s: str) -> str:
    """Convert column names to camelCase exactly like original PC FileLoader"""
    s = s.strip()
    # Replace common special characters with safe equivalents
    s = s.replace('%', 'Pct').replace('&', 'n')
    if not _DELIMS.search(s) and not _UPPER.match(s):
        return s[0].lower() + s[1:]
    parts = [p for p in _DELIMS.split(s) if p]
    column_name = parts[0].lower() + ''.join(custom_cap(p) for p in parts[1:])
    return column_name

def clean_columns_spark(df):
    """Clean DataFrame columns using original PC FileLoader logic"""
    seen = {}
    columns_to_drop = []
    columns_to_rename = []

    # First pass: identify what to rename and what to drop
    for c in df.columns:
        if c not in ["filename", "source_folder", "ingestionTimestamp", "source_entity", "environment", "file_type"]:
            new = to_camel_case(c)
            if new not in seen:
                if new != c:  # Only rename if different
                    columns_to_rename.append((c, new))
                seen[new] = c
            else:
                # Mark duplicate columns for dropping
                columns_to_drop.append(c)

    # Apply renames
    for old_col, new_col in columns_to_rename:
        df = df.withColumnRenamed(old_col, new_col)

    # Drop duplicate columns
    for col_to_drop in columns_to_drop:
        df = df.drop(col_to_drop)

    return df

# COMMAND ----------

# MAGIC %md
# MAGIC ## Silver Layer - Cleaned Data with Proper Logic

# COMMAND ----------

@dlt.table(
    name="silver_files_cleaned",
    comment="Universal silver table with cleaned data matching original PC FileLoader logic",
    table_properties={
        "quality": "silver",
        "pipelines.autoOptimize.managed": "true"
    }
)
@dlt.expect_or_drop("valid_source_table", "source_table_name IS NOT NULL")
@dlt.expect("data_freshness", "ingestionTimestamp > current_timestamp() - INTERVAL 7 DAYS")
def silver_files_cleaned():
    """
    Universal silver table that cleans and processes all bronze data.
    Uses the EXACT same column cleaning logic as original PC FileLoader.
    """
    bronze_df = dlt.read("bronze_files_raw")

    # Apply the original PC FileLoader column cleaning logic
    cleaned_df = clean_columns_spark(bronze_df)

    return (
        cleaned_df
        .withColumn("silver_processed_at", current_timestamp())
        .withColumn("data_quality_score", lit(1.0))
        .withColumn("source_table_name", col("source_folder"))
        .withColumn("record_hash", sha2(concat_ws("|", *[col(c) for c in cleaned_df.columns
                                                        if c not in ["ingestionTimestamp", "filename", "silver_processed_at"]]), 256))
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Individual Source Views (Bronze Layer)

# COMMAND ----------

# Create individual bronze views for each configured source
# This gives you the familiar table-per-source structure while using the universal table underneath

@dlt.view(
    name="bronze_xva_consolidated_report",
    comment="Bronze view for XVA Consolidated Report data"
)
def bronze_xva_consolidated_report():
    """Individual view for XVA Consolidated Report from universal bronze table"""
    return (
        dlt.read("bronze_files_raw")
        .filter(lower(col("source_folder")) == "xvaconsolidatedreport")
    )

@dlt.view(
    name="bronze_xva_input_report",
    comment="Bronze view for XVA Input Report data"
)
def bronze_xva_input_report():
    """Individual view for XVA Input Report from universal bronze table"""
    return (
        dlt.read("bronze_files_raw")
        .filter(lower(col("source_folder")) == "xvacurateddataset_inputreport")
    )

@dlt.view(
    name="bronze_xva_mtm_reconciliation",
    comment="Bronze view for XVA MTM Reconciliation data"
)
def bronze_xva_mtm_reconciliation():
    """Individual view for XVA MTM Reconciliation from universal bronze table"""
    return (
        dlt.read("bronze_files_raw")
        .filter(lower(col("source_folder")) == "xvacurateddataset_mtmreconciliation")
    )

@dlt.view(
    name="bronze_xva_missing_trades",
    comment="Bronze view for XVA Missing Trades data"
)
def bronze_xva_missing_trades():
    """Individual view for XVA Missing Trades from universal bronze table"""
    return (
        dlt.read("bronze_files_raw")
        .filter(lower(col("source_folder")) == "xvacurateddataset_missingtradesreport")
    )

@dlt.view(
    name="bronze_trade_data_reconciliation",
    comment="Bronze view for Trade Data Reconciliation"
)
def bronze_trade_data_reconciliation():
    """Individual view for Trade Data Reconciliation from universal bronze table"""
    return (
        dlt.read("bronze_files_raw")
        .filter(lower(col("source_folder")) == "tradedatareconciliation")
    )

@dlt.view(
    name="bronze_metal_exposures",
    comment="Bronze view for Metal Exposures data"
)
def bronze_metal_exposures():
    """Individual view for Metal Exposures from universal bronze table"""
    return (
        dlt.read("bronze_files_raw")
        .filter(lower(col("source_folder")) == "metalexposures")
    )

@dlt.view(
    name="bronze_energy_exposures",
    comment="Bronze view for Energy Exposures data"
)
def bronze_energy_exposures():
    """Individual view for Energy Exposures from universal bronze table"""
    return (
        dlt.read("bronze_files_raw")
        .filter(lower(col("source_folder")) == "energyexposures")
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Silver Tables with Key-based Deduplication (Matching Original Logic)

# COMMAND ----------

# Create individual silver tables with proper deduplication based on pipelinecontrol KeyColumn
# This matches the original PC FileLoader silver table logic

@dlt.table(
    name="silver_xva_consolidated_report",
    comment="Silver table for XVA Consolidated Report with key-based deduplication",
    table_properties={
        "quality": "silver",
        "pipelines.autoOptimize.managed": "true"
    }
)
@dlt.expect_or_drop("valid_source_table", "source_table_name IS NOT NULL")
@dlt.expect("data_freshness", "ingestionTimestamp > current_timestamp() - INTERVAL 7 DAYS")
def silver_xva_consolidated_report():
    """
    Silver table with key-based deduplication matching original PC FileLoader logic.
    Filters null key values and deduplicates based on KeyColumn from pipelinecontrol.
    """
    silver_df = (
        dlt.read("silver_files_cleaned")
        .filter(lower(col("source_table_name")) == "xvaconsolidatedreport")
    )

    # Get key columns from configuration (would need to be parameterized based on pipelinecontrol)
    # For now, using common key patterns - this should be dynamic based on pipelinecontrol.KeyColumn
    key_columns = ["valueDate", "counterparty"]  # Example keys - should come from config

    # Filter out null key values (matching original logic)
    for key_col in key_columns:
        if key_col in silver_df.columns:
            silver_df = silver_df.filter(col(key_col).isNotNull())

    # Deduplicate based on key columns (matching original logic)
    available_keys = [k for k in key_columns if k in silver_df.columns]
    if available_keys:
        silver_df = silver_df.dropDuplicates(available_keys)

    return silver_df

@dlt.view(
    name="silver_xva_input_report",
    comment="Silver view for XVA Input Report - cleaned data"
)
def silver_xva_input_report():
    """Individual silver view for XVA Input Report"""
    return (
        dlt.read("silver_files_cleaned")
        .filter(lower(col("source_table_name")) == "xvacurateddataset_inputreport")
    )

@dlt.view(
    name="silver_xva_mtm_reconciliation",
    comment="Silver view for XVA MTM Reconciliation - cleaned data"
)
def silver_xva_mtm_reconciliation():
    """Individual silver view for XVA MTM Reconciliation"""
    return (
        dlt.read("silver_files_cleaned")
        .filter(lower(col("source_table_name")) == "xvacurateddataset_mtmreconciliation")
    )

@dlt.view(
    name="silver_xva_missing_trades",
    comment="Silver view for XVA Missing Trades - cleaned data"
)
def silver_xva_missing_trades():
    """Individual silver view for XVA Missing Trades"""
    return (
        dlt.read("silver_files_cleaned")
        .filter(lower(col("source_table_name")) == "xvacurateddataset_missingtradesreport")
    )

@dlt.view(
    name="silver_trade_data_reconciliation",
    comment="Silver view for Trade Data Reconciliation - cleaned data"
)
def silver_trade_data_reconciliation():
    """Individual silver view for Trade Data Reconciliation"""
    return (
        dlt.read("silver_files_cleaned")
        .filter(lower(col("source_table_name")) == "tradedatareconciliation")
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Configuration Mapping

# COMMAND ----------

@dlt.table(
    name="source_table_mapping",
    comment="Mapping between detected folders and pipeline configuration"
)
def source_table_mapping():
    """
    Map detected source folders to pipeline configuration.
    This shows which configured tables have data and which don't.
    """
    detected = dlt.read("processing_summary").select(
        col("source_folder").alias("detected_folder"),
        col("total_records"),
        col("unique_files")
    )

    configured = dlt.read("active_pipeline_configs").select(
        lower(col("SourceTableName")).alias("configured_table"),
        col("SourceTableName"),
        col("TargetTableName"),
        col("IsSilver"),
        col("Description")
    )

    return (
        configured.join(
            detected,
            configured.configured_table == detected.detected_folder,
            "left_outer"
        )
        .withColumn("has_data", when(col("total_records").isNotNull(), True).otherwise(False))
        .withColumn("mapping_timestamp", current_timestamp())
        .select(
            col("SourceTableName"),
            col("TargetTableName"),
            col("detected_folder"),
            col("has_data"),
            col("total_records"),
            col("unique_files"),
            col("IsSilver"),
            col("Description"),
            col("mapping_timestamp")
        )
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Pipeline Execution Summary

# COMMAND ----------

@dlt.table(
    name="pipeline_execution_summary",
    comment="Overall pipeline execution summary and health check"
)
def pipeline_execution_summary():
    """
    Create an overall summary of pipeline execution.
    This replaces the complex logging in the original notebook.
    """

    # Get summary stats
    mapping_df = dlt.read("source_table_mapping")
    quality_df = dlt.read("data_quality_checks")

    summary_stats = mapping_df.agg(
        count("*").alias("total_configured_tables"),
        sum(when(col("has_data"), 1).otherwise(0)).alias("tables_with_data"),
        sum(coalesce(col("total_records"), lit(0))).alias("total_records_processed"),
        sum(coalesce(col("unique_files"), lit(0))).alias("total_files_processed")
    ).collect()[0]

    quality_stats = quality_df.agg(
        avg("quality_score").alias("avg_quality_score"),
        count("*").alias("total_records_checked")
    ).collect()[0]

    # Create summary record with null safety
    tables_with_data = summary_stats["tables_with_data"] if summary_stats["tables_with_data"] is not None else 0
    total_configured = summary_stats["total_configured_tables"] if summary_stats["total_configured_tables"] is not None else 0
    total_records = summary_stats["total_records_processed"] if summary_stats["total_records_processed"] is not None else 0
    total_files = summary_stats["total_files_processed"] if summary_stats["total_files_processed"] is not None else 0
    avg_quality = quality_stats["avg_quality_score"] if quality_stats["avg_quality_score"] is not None else 0.0
    records_checked = quality_stats["total_records_checked"] if quality_stats["total_records_checked"] is not None else 0

    summary_data = [(
        entity,
        env,
        total_configured,
        tables_with_data,
        total_records,
        total_files,
        avg_quality,
        records_checked,
        "SUCCESS" if tables_with_data > 0 else "NO_DATA",
        spark.conf.get("spark.databricks.clusterUsageTags.runId", "unknown")
    )]

    return (
        spark.createDataFrame(summary_data, [
            "entity", "environment", "configured_tables", "tables_with_data",
            "total_records", "total_files", "avg_quality_score", "records_checked",
            "pipeline_status", "run_id"
        ])
        .withColumn("execution_timestamp", current_timestamp())
        .withColumn("pipeline_type", lit("pc_fileloader_replacement"))
    )
