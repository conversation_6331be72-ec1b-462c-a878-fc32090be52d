# Databricks notebook source
# MAGIC %md
# MAGIC # PC FileLoader - Dynamic Lakeflow Pipeline
# MAGIC 
# MAGIC This pipeline replaces PC With FileLoader.py using Lakeflow declarative approach.
# MAGIC It dynamically reads from pipelinecontrol table and creates Bronze/Silver tables accordingly.
# MAGIC 
# MAGIC **Parameters:**
# MAGIC - entity: XVA (configurable)
# MAGIC - env: dev (configurable)

# COMMAND ----------

import dlt
import re
from pyspark.sql.functions import *
from pyspark.sql.types import *

# COMMAND ----------

# MAGIC %md
# MAGIC ## Configuration

# COMMAND ----------

# Pipeline parameters - can be set via pipeline configuration
entity = spark.conf.get("entity", "XVA")
env = spark.conf.get("env", "dev")

# Paths
cleaned_files_base = f"/Volumes/dbw_dev_itdev_test_uks/bronze_xva/inbound_cleanfiles/{entity}/"
pipelinecontrol_table = "dbw_dev_itdev_test_uks.utility_metadata.pipelinecontrol"
schema_location_base = "/Volumes/dbw_dev_itdev_test_uks/bronze_xva/pipeline_schemas/"

print(f"🔧 Entity: {entity}, Environment: {env}")
print(f"📁 Source: {cleaned_files_base}")

# COMMAND ----------

# MAGIC %md
# MAGIC ## Pipeline Configuration Source

# COMMAND ----------

@dlt.table(
    name="active_pipeline_configs",
    comment="Active pipeline configurations from pipelinecontrol table"
)
def active_pipeline_configs():
    """
    Read active pipeline configurations for the specified entity.
    This drives the dynamic table creation.
    """
    return (
        spark.table(pipelinecontrol_table)
        .filter(
            (col("Entity") == entity) & 
            (col("IsActive") == True) &
            (col("Source") == "File") &
            (col("SourceType").isin(["csv", "parquet", "xlsx"]))
        )
        .select(
            col("EntityId"),
            col("Entity"),
            col("SourceTableName"),
            col("TargetTableName"),
            col("SchemaName"),
            col("RefinedSchemaName"),
            col("SourceType"),
            col("KeyColumn"),
            col("IsSilver"),
            col("Description"),
            col("Input_FilePath")
        )
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## File Detection

# COMMAND ----------

@dlt.table(
    name="detected_files",
    comment="Files detected in cleaned files directory"
)
def detected_files():
    """
    Detect all files in the cleaned files directory using Auto Loader.
    This provides the source for all bronze tables.
    """
    return (
        spark.readStream
        .format("cloudFiles")
        .option("cloudFiles.format", "binaryFile")
        .option("cloudFiles.useNotifications", "false")
        .option("cloudFiles.includeExistingFiles", "true")
        .option("cloudFiles.schemaLocation", f"{schema_location_base}detected_files")
        .load(cleaned_files_base)
        .select(
            col("path").alias("file_path"),
            col("modificationTime").alias("file_modification_time"),
            col("length").alias("file_size_bytes"),
            regexp_extract(col("path"), r"([^/]+)$", 1).alias("file_name"),
            regexp_extract(col("path"), r"/([^/]+)/[^/]+$", 1).alias("folder_name"),
            regexp_extract(col("path"), r"\.([^.]+)$", 1).alias("file_extension"),
            current_timestamp().alias("detected_at")
        )
        .filter(col("file_name").rlike(r"\.(csv|xlsx|parquet)$"))
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Universal Bronze Table

# COMMAND ----------

@dlt.table(
    name="bronze_files_raw",
    comment="Universal bronze table for all file types - raw data with metadata",
    table_properties={
        "quality": "bronze",
        "pipelines.autoOptimize.managed": "true"
    }
)
def bronze_files_raw():
    """
    Universal bronze table that reads all CSV files from all folders.
    This replaces the need for individual bronze tables per source.
    """
    return (
        spark.readStream
        .format("cloudFiles")
        .option("cloudFiles.format", "csv")
        .option("cloudFiles.useNotifications", "false")
        .option("cloudFiles.includeExistingFiles", "true")
        .option("cloudFiles.schemaLocation", f"{schema_location_base}bronze_universal")
        .option("header", "true")
        .option("inferSchema", "true")
        .option("multiline", "true")
        .option("escape", '"')
        .option("recursiveFileLookup", "true")
        .load(cleaned_files_base)
        .withColumn("filename", col("_metadata.file_path"))
        .withColumn("source_folder", regexp_extract(col("_metadata.file_path"), r"/([^/]+)/[^/]+$", 1))
        .withColumn("ingestionTimestamp", current_timestamp())
        .withColumn("source_entity", lit(entity))
        .withColumn("environment", lit(env))
        .withColumn("file_type", lit("csv"))
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Dynamic Bronze Views

# COMMAND ----------

# Helper function to clean column names
def clean_column_names_sql(table_name: str) -> str:
    """Generate SQL to clean column names"""
    return f"""
    SELECT *,
           '{table_name}' as source_table_name
    FROM bronze_files_raw 
    WHERE lower(source_folder) = lower('{table_name}')
    """

# COMMAND ----------

# MAGIC %md
# MAGIC ## Processing Metrics

# COMMAND ----------

@dlt.table(
    name="processing_summary",
    comment="Summary of files processed by source table"
)
def processing_summary():
    """
    Create processing summary showing files processed per source table.
    This replaces individual table metrics.
    """
    return (
        dlt.read("bronze_files_raw")
        .groupBy("source_folder", "source_entity", "environment")
        .agg(
            count("*").alias("total_records"),
            countDistinct("filename").alias("unique_files"),
            max("ingestionTimestamp").alias("last_processed"),
            min("ingestionTimestamp").alias("first_processed")
        )
        .withColumn("processing_date", current_date())
        .withColumn("pipeline_run_id", lit(spark.conf.get("spark.databricks.clusterUsageTags.runId", "unknown")))
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Data Quality Monitoring

# COMMAND ----------

@dlt.table(
    name="data_quality_checks",
    comment="Data quality checks across all source tables"
)
@dlt.expect_or_drop("valid_filename", "filename IS NOT NULL")
@dlt.expect_or_drop("valid_folder", "source_folder IS NOT NULL")
@dlt.expect("data_freshness", "ingestionTimestamp > current_timestamp() - INTERVAL 7 DAYS")
def data_quality_checks():
    """
    Perform data quality checks on the universal bronze table.
    """
    return (
        dlt.read("bronze_files_raw")
        .withColumn("record_count", lit(1))
        .withColumn("has_null_filename", when(col("filename").isNull(), 1).otherwise(0))
        .withColumn("has_null_folder", when(col("source_folder").isNull(), 1).otherwise(0))
        .withColumn("quality_score", 
                   when((col("filename").isNotNull()) & (col("source_folder").isNotNull()), 1.0)
                   .otherwise(0.0))
        .withColumn("check_timestamp", current_timestamp())
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Silver Layer - Cleaned Data

# COMMAND ----------

@dlt.table(
    name="silver_files_cleaned",
    comment="Universal silver table with cleaned data and quality checks",
    table_properties={
        "quality": "silver",
        "pipelines.autoOptimize.managed": "true"
    }
)
@dlt.expect_or_drop("valid_source_table", "source_table_name IS NOT NULL")
@dlt.expect("data_freshness", "ingestionTimestamp > current_timestamp() - INTERVAL 7 DAYS")
def silver_files_cleaned():
    """
    Universal silver table that cleans and processes all bronze data.
    Column names are cleaned and data quality is applied.
    """
    bronze_df = dlt.read("bronze_files_raw")

    # Clean all column names
    cleaned_df = bronze_df
    for col_name in bronze_df.columns:
        if col_name not in ["filename", "source_folder", "ingestionTimestamp", "source_entity", "environment", "file_type"]:
            clean_name = re.sub(r'[^a-zA-Z0-9_]', '_', col_name)
            clean_name = re.sub(r'_+', '_', clean_name)  # Replace multiple underscores
            clean_name = clean_name.strip('_')  # Remove leading/trailing underscores
            if clean_name != col_name and clean_name:
                cleaned_df = cleaned_df.withColumnRenamed(col_name, clean_name)

    return (
        cleaned_df
        .withColumn("silver_processed_at", current_timestamp())
        .withColumn("data_quality_score", lit(1.0))
        .withColumn("source_table_name", col("source_folder"))
        .withColumn("record_hash", sha2(concat_ws("|", *[col(c) for c in cleaned_df.columns
                                                        if c not in ["ingestionTimestamp", "filename", "silver_processed_at"]]), 256))
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Configuration Mapping

# COMMAND ----------

@dlt.table(
    name="source_table_mapping",
    comment="Mapping between detected folders and pipeline configuration"
)
def source_table_mapping():
    """
    Map detected source folders to pipeline configuration.
    This shows which configured tables have data and which don't.
    """
    detected = dlt.read("processing_summary").select(
        col("source_folder").alias("detected_folder"),
        col("total_records"),
        col("unique_files")
    )

    configured = dlt.read("active_pipeline_configs").select(
        lower(col("SourceTableName")).alias("configured_table"),
        col("SourceTableName"),
        col("TargetTableName"),
        col("IsSilver"),
        col("Description")
    )

    return (
        configured.join(
            detected,
            configured.configured_table == detected.detected_folder,
            "left_outer"
        )
        .withColumn("has_data", when(col("total_records").isNotNull(), True).otherwise(False))
        .withColumn("mapping_timestamp", current_timestamp())
        .select(
            col("SourceTableName"),
            col("TargetTableName"),
            col("detected_folder"),
            col("has_data"),
            col("total_records"),
            col("unique_files"),
            col("IsSilver"),
            col("Description"),
            col("mapping_timestamp")
        )
    )

# COMMAND ----------

# MAGIC %md
# MAGIC ## Pipeline Execution Summary

# COMMAND ----------

@dlt.table(
    name="pipeline_execution_summary",
    comment="Overall pipeline execution summary and health check"
)
def pipeline_execution_summary():
    """
    Create an overall summary of pipeline execution.
    This replaces the complex logging in the original notebook.
    """

    # Get summary stats
    mapping_df = dlt.read("source_table_mapping")
    quality_df = dlt.read("data_quality_checks")

    summary_stats = mapping_df.agg(
        count("*").alias("total_configured_tables"),
        sum(when(col("has_data"), 1).otherwise(0)).alias("tables_with_data"),
        sum(coalesce(col("total_records"), lit(0))).alias("total_records_processed"),
        sum(coalesce(col("unique_files"), lit(0))).alias("total_files_processed")
    ).collect()[0]

    quality_stats = quality_df.agg(
        avg("quality_score").alias("avg_quality_score"),
        count("*").alias("total_records_checked")
    ).collect()[0]

    # Create summary record with null safety
    tables_with_data = summary_stats["tables_with_data"] if summary_stats["tables_with_data"] is not None else 0
    total_configured = summary_stats["total_configured_tables"] if summary_stats["total_configured_tables"] is not None else 0
    total_records = summary_stats["total_records_processed"] if summary_stats["total_records_processed"] is not None else 0
    total_files = summary_stats["total_files_processed"] if summary_stats["total_files_processed"] is not None else 0
    avg_quality = quality_stats["avg_quality_score"] if quality_stats["avg_quality_score"] is not None else 0.0
    records_checked = quality_stats["total_records_checked"] if quality_stats["total_records_checked"] is not None else 0

    summary_data = [(
        entity,
        env,
        total_configured,
        tables_with_data,
        total_records,
        total_files,
        avg_quality,
        records_checked,
        "SUCCESS" if tables_with_data > 0 else "NO_DATA",
        spark.conf.get("spark.databricks.clusterUsageTags.runId", "unknown")
    )]

    return (
        spark.createDataFrame(summary_data, [
            "entity", "environment", "configured_tables", "tables_with_data",
            "total_records", "total_files", "avg_quality_score", "records_checked",
            "pipeline_status", "run_id"
        ])
        .withColumn("execution_timestamp", current_timestamp())
        .withColumn("pipeline_type", lit("pc_fileloader_replacement"))
    )
