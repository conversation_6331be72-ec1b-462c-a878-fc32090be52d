import re
from datetime import datetime
import pandas as pd
from pyspark.sql import SparkSession
from databricks.sdk.runtime import dbutils
import os

# Parameters (these should be set via LakeFlow pipeline parameters)
env = os.environ.get("env", "dev").lower()
entity = os.environ.get("entity", "").upper()

# Initialize Spark session
spark = SparkSession.builder.appName("FileCleaning").getOrCreate()

# Define source and destination directories
if env == "dev":
    source_directory = "abfss://<EMAIL>/home/<USER>/"
elif env == "prod":
    source_directory = "abfss://<EMAIL>/home/<USER>/"
else:
    raise ValueError(f"Unknown environment: {env}")

destination_directory = f"/Volumes/mbcl_{env}_bronze/{entity.lower()}/inbound_cleanfiles/"
if not dbutils.fs.mkdirs(destination_directory):
    print(f"Failed to create directory: {destination_directory}")

# List files in the source directory
files = dbutils.fs.ls(source_directory)

date_pattern = re.compile(r'\d-\d-\dT\d:\d:\d')
non_alphanumeric_pattern = re.compile(r'[^a-zA-Z0-9]')
numeric_pattern = re.compile(r'\d')
non_alpha_pattern = re.compile(r'[^a-zA-Z]')

def clean_file_name(file_name):
    first_part = file_name[:19]
    if date_pattern.match(first_part):
        file_name = file_name[20:]
        first_part = ''
    else:
        first_part = non_alphanumeric_pattern.sub('', file_name[:15])
    has_number = any(char.isdigit() for char in first_part)
    if not has_number:
        parts = file_name.split('_')
        if len(parts) > 2:
            parts[2] = numeric_pattern.sub('', parts[2])
            file_name = '_'.join(parts)
        else:
            file_name = '_'.join(parts)
    if has_number:
        first_part += '_'
    second_part = non_alpha_pattern.sub('', file_name)
    cleaned_name = second_part
    cleaned_file_name = cleaned_name + '.csv'
    return cleaned_file_name

def extract_directory_name(cleaned_file_name):
    if cleaned_file_name.endswith('.csv'):
        cleaned_file_name = cleaned_file_name[:-4]
    dir_name = numeric_pattern.sub('', cleaned_file_name)
    dir_name = dir_name.replace(' ', '')
    dir_name = non_alpha_pattern.sub('', dir_name)
    return dir_name.lower()

log_entries = []

def directory_exists(path):
    try:
        dbutils.fs.ls(path)
        return True
    except:
        return False

if files:
    for file in files:
        if file.isDir():
            continue
        file_name = file.name
        cleaned_file_name = clean_file_name(file_name.rsplit('.', 1)[0])
        folder_name = extract_directory_name(cleaned_file_name)
        destination_directory_for_file = f"{destination_directory}/{entity}/{folder_name}/"
        source_path = f"{source_directory}{file_name}"
        destination_path = f"{destination_directory_for_file}{cleaned_file_name}"
        dbutils.fs.mv(source_path, destination_path)
        print(f"Moved {file_name} to {destination_path}")
        log_entries.append({
            "OldFileName": file_name,
            "CleanedFileName": cleaned_file_name,
            "FolderName": folder_name,
            "DateOperation": datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        })
else:
    print("No files found in the source directory.")

if log_entries:
    log_df = spark.createDataFrame(pd.DataFrame(log_entries))
    log_df.write.format("delta").mode("overwrite").saveAsTable(f"mbcl_{env}_utility.metadata.cleanedFileLogs")
else:
    print("No log entries to write to DataFrame.")
